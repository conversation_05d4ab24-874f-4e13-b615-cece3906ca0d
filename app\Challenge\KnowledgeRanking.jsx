import React from 'react';
import { StyleSheet, View, TouchableOpacity, Image, ScrollView } from 'react-native';
import { useRouter } from 'expo-router';
import ThemedText from '../../components/ThemedText';
import ThemedView from '../../components/ThemedView';

const KnowledgeRanking = () => {
  const router = useRouter();

  // 排名数据
  const rankingData = [
    { rank: 1, name: '歪歪想听歌', avatar: require('../../assets/Community_image/AvatarFour.png') },
    { rank: 2, name: '首席摸鱼顾问', avatar: require('../../assets/Community_image/AvatarFive.png') },
    { rank: 3, name: 'Jessica', avatar: require('../../assets/Community_image/HandsomeAvatar.png'), highlighted: true },
    { rank: 4, name: '喵喵喵', avatar: require('../../assets/Community_image/AvatarSeven.png') },
    { rank: 5, name: 'kin', avatar: require('../../assets/Community_image/AvatarEight.png') },
    { rank: 6, name: 'carrry', avatar: require('../../assets/Community_image/AvatarSix.png') },
  ];

  return (
    <ThemedView style={styles.container}>
      {/* 安全视图 */}
      <View style={styles.safeArea} />

      <ScrollView style={styles.scrollView} contentContainerStyle={{ paddingTop: 60 }} showsVerticalScrollIndicator={false}>
        {/* 顶部标题栏 */}
        <View style={styles.header}>
          <TouchableOpacity onPress={() => router.back()} style={styles.backButton}>
            <Image source={require('../../assets/FrameTwo.png')} style={styles.backIcon} />
          </TouchableOpacity>
          <ThemedText style={styles.headerTitle}>积分排名</ThemedText>
          <View style={styles.headerRight} />
        </View>

        {/* 前三名颁奖台 */}
        <View style={styles.podiumContainer}>
          {/* 第二名 */}
          <View style={styles.podiumItem}>
            <View style={styles.avatarContainer}>
              <Image
                source={require('../../assets/Community_image/AvatarFive.png')}
                style={styles.avatar}
                resizeMode="cover"
              />
            </View>
            <View style={[styles.podium, styles.podiumSecond]}>
              <ThemedText style={styles.podiumNumber}>2</ThemedText>
            </View>
          </View>

          {/* 第一名 */}
          <View style={styles.podiumItem}>
            <View style={styles.avatarContainer}>
              <Image
                source={require('../../assets/Community_image/AvatarFour.png')}
                style={styles.avatar}
                resizeMode="cover"
              />
            </View>
            <View style={[styles.podium, styles.podiumFirst]}>
              <ThemedText style={styles.podiumNumber}>1</ThemedText>
            </View>
          </View>

          {/* 第三名 */}
          <View style={styles.podiumItem}>
            <View style={styles.avatarContainer}>
              <Image
                source={require('../../assets/Community_image/HandsomeAvatar.png')}
                style={styles.avatar}
                resizeMode="cover"
              />
            </View>
            <View style={[styles.podium, styles.podiumThird]}>
              <ThemedText style={styles.podiumNumber}>3</ThemedText>
            </View>
          </View>
        </View>

        {/* 排名列表 */}
        <View style={styles.rankingList}>
          {rankingData.map((item, index) => (
            <View
              key={index}
              style={[
                styles.rankingItem,
                item.highlighted && styles.rankingItemHighlighted
              ]}
            >
              <ThemedText style={styles.rankNumber}>{item.rank.toString().padStart(2, '0')}</ThemedText>
              <Image
                source={item.avatar}
                style={styles.avatar}
                resizeMode="cover"
              />
              <ThemedText style={styles.userName}>{item.name}</ThemedText>
            </View>
          ))}
        </View>
      </ScrollView>
    </ThemedView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFF8F3',
  },
  safeArea: {
    height: 50,
    backgroundColor: '#FFF8F3',
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    zIndex: 15,
  },
  scrollView: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingBottom: 16,
    paddingTop: 0,
    backgroundColor: 'transparent',
  },
  backButton: {
    padding: 8,
  },
  backIcon: {
    width: 24,
    height: 24,
    resizeMode: 'contain',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  headerRight: {
    width: 32,
  },
  podiumContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'flex-end',
    paddingHorizontal: 40,
    paddingVertical: 40,
    gap: 20,
  },
  podiumItem: {
    alignItems: 'center',
    flex: 1,
  },
  avatarContainer: {
    marginBottom: 10,
  },
  avatar: {
    width: 50,
    height: 50,
    borderRadius: 25,
  },
  podium: {
    width: 80,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 8,
  },
  podiumFirst: {
    height: 100,
    backgroundColor: '#FF8A80',
  },
  podiumSecond: {
    height: 80,
    backgroundColor: '#FFD54F',
  },
  podiumThird: {
    height: 60,
    backgroundColor: '#81D4FA',
  },
  podiumNumber: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#fff',
  },
  rankingList: {
    backgroundColor: '#F5E6D3',
    borderTopLeftRadius: 30,
    borderTopRightRadius: 30,
    paddingHorizontal: 20,
    paddingTop: 30,
    paddingBottom: 40,
    gap: 12,
  },
  rankingItem: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    gap: 16,
  },
  rankingItemHighlighted: {
    borderWidth: 2,
    borderColor: '#FF8A80',
  },
  rankNumber: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    minWidth: 30,
  },
  userName: {
    fontSize: 16,
    color: '#333',
    flex: 1,
  },
});

export default KnowledgeRanking;