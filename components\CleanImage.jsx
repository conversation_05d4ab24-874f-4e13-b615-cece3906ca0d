// components/CleanImage.jsx - 专门处理URL格式问题的图片组件
import React, { useState, useMemo } from 'react';
import { Image, View, Platform } from 'react-native';
import { transformImageUrl } from '../lib/imageUtils';
import ThemedText from './ThemedText';

const CleanImage = ({ 
  source, 
  style, 
  placeholder = 'https://via.placeholder.com/300x200?text=No+Image',
  showDebugInfo = false,
  onError,
  onLoad,
  ...props 
}) => {
  const [imageError, setImageError] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [debugInfo, setDebugInfo] = useState('');

  // 使用useMemo缓存URL处理结果，避免重复计算
  const { processedUrl, debugText } = useMemo(() => {
    if (!source) {
      return {
        processedUrl: placeholder,
        debugText: '无源图片'
      };
    }

    let originalUrl = '';
    if (typeof source === 'string') {
      originalUrl = source;
    } else if (source?.uri) {
      originalUrl = source.uri;
    } else {
      return {
        processedUrl: placeholder,
        debugText: '无效的图片源'
      };
    }

    try {
      // 直接使用transformImageUrl，它内部已经包含了清理逻辑
      const finalUrl = transformImageUrl(originalUrl);

      return {
        processedUrl: finalUrl,
        debugText: `原始: ${originalUrl}\n最终: ${finalUrl}`
      };
    } catch (error) {
      console.error(`[${Platform.OS}] 图片URL处理失败:`, error);
      return {
        processedUrl: placeholder,
        debugText: `处理失败: ${error.message}`
      };
    }
  }, [source, placeholder]);

  // 更新debugInfo状态（仅在值变化时）
  React.useEffect(() => {
    setDebugInfo(debugText);
  }, [debugText]);

  // 获取最终的图片源
  const getImageSource = () => {
    if (typeof source === 'object' && source.uri) {
      return { ...source, uri: processedUrl };
    }
    
    return { uri: processedUrl };
  };

  const handleError = (error) => {
    console.error(`[${Platform.OS}] 图片加载失败:`, {
      originalSource: source,
      processedUrl: processedUrl,
      error: error.nativeEvent?.error || error
    });
    
    setImageError(true);
    setIsLoading(false);
    
    if (onError) {
      onError(error);
    }
  };

  const handleLoad = (event) => {
    console.log(`[${Platform.OS}] 图片加载成功:`, processedUrl);
    setIsLoading(false);
    setImageError(false);
    
    if (onLoad) {
      onLoad(event);
    }
  };

  const handleLoadStart = () => {
    setIsLoading(true);
    setImageError(false);
  };

  // 如果图片加载失败，显示占位符
  if (imageError) {
    return (
      <View style={[style, { justifyContent: 'center', alignItems: 'center', backgroundColor: '#f0f0f0' }]}>
        <ThemedText style={{ fontSize: 12, color: '#666' }}>图片加载失败</ThemedText>
        {showDebugInfo && (
          <ThemedText style={{ fontSize: 10, color: '#999', marginTop: 5, textAlign: 'center' }}>
            {debugInfo}
          </ThemedText>
        )}
      </View>
    );
  }

  return (
    <View style={style}>
      <Image
        source={getImageSource()}
        style={[{ width: '100%', height: '100%' }, style]}
        onError={handleError}
        onLoad={handleLoad}
        onLoadStart={handleLoadStart}
        {...props}
      />
      
      {/* 调试信息 */}
      {showDebugInfo && (
        <View style={{
          position: 'absolute',
          bottom: 0,
          left: 0,
          right: 0,
          backgroundColor: 'rgba(0,0,0,0.7)',
          padding: 5
        }}>
          <ThemedText style={{ fontSize: 9, color: 'white' }}>
            {debugInfo}
          </ThemedText>
        </View>
      )}
      
      {/* 加载指示器 */}
      {isLoading && (
        <View style={{
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          justifyContent: 'center',
          alignItems: 'center',
          backgroundColor: 'rgba(255,255,255,0.8)'
        }}>
          <ThemedText style={{ fontSize: 12, color: '#666' }}>加载中...</ThemedText>
        </View>
      )}
    </View>
  );
};

export default CleanImage;
