import React, { useState, useRef, useEffect } from 'react';
import { StyleSheet, View, TextInput, Image, ScrollView, TouchableOpacity, Animated, PanResponder, Platform } from 'react-native'
import { useRouter } from 'expo-router';
import Spacer from "../../components/Spacer"
import ThemedText from "../../components/ThemedText"
import ThemedView from "../../components/ThemedView"
import ThemedCard from "../../components/ThemedCard"
import ThemedTextInput from "../../components/ThemedTextInput"
import { Colors } from "../../constants/Colors"
import { LinearGradient } from 'expo-linear-gradient'
import apiServices from '../../lib/apiServices'
import WebCompatibleView from '../../components/WebCompatibleView'
import CachedImage from '../../components/CachedImage'
import { useGuestPermission } from '../../hooks/useGuestPermission'
import GuestPermissionModal from '../../components/GuestPermissionModal'

const mascot = require('../../assets/dog.png') // 吉祥物图片
const cardImg = require('../../assets/12345.jpg') // 卡片顶部图片
const favicon = require('../../assets/favicon.png') // 服务按钮右半图片
// 在顶部引入搜索图标
const searchIcon = require('../../assets/Index_image/Search.png');

const tabs = ['视频', '训练', '挑战']
const services = [
  { label: '同步教材' },
  { label: '网课学习' },
  { label: '知识归纳' },
]
const cards = [1, 2, 3, 4, 5, 6]

const Home = () => {
  const [activeTab, setActiveTab] = useState('视频');
  const [activeGoalTab, setActiveGoalTab] = useState('考试');
  const [scrollEnabled, setScrollEnabled] = useState(true); // 新增
  const [featuredCourses, setFeaturedCourses] = useState([]); // 精品课程数据
  const [hotCourses, setHotCourses] = useState([]); // 热门课程数据
  const [latestCourses, setLatestCourses] = useState([]); // 最新课程数据
  const [recommendVideos, setRecommendVideos] = useState([]); // 推荐视频数据
  const [loading, setLoading] = useState(false); // 加载状态
  const [isInitialized, setIsInitialized] = useState(false); // 初始化状态
  const router = useRouter();

  // 游客权限管理
  const {
    wrapGuestAction,
    showPermissionModal,
    currentFeature,
    handleRegister,
    closePermissionModal
  } = useGuestPermission();

  // 格式化数字显示（如播放量）
  const formatNumber = (num) => {
    if (num === null || num === undefined) return '0';
    if (num === 0) return '0';
    if (num >= 10000) {
      return (num / 10000).toFixed(1) + '万';
    }
    return num.toString();
  };

  // 添加淡入动画
  const fadeAnimation = useRef(new Animated.Value(1)).current;
  // 目标卡片初始化动画
  const goalWrapperOpacity = useRef(new Animated.Value(0)).current;

  // 初始化效果，避免Web端初始渲染错位
  useEffect(() => {
    // 延迟一帧确保DOM完全渲染
    const timer = setTimeout(() => {
      setIsInitialized(true);
    }, 100); // 增加延迟时间确保布局稳定

    return () => clearTimeout(timer);
  }, []);

  // 处理布局完成事件
  const handleGoalWrapperLayout = () => {
    if (!isInitialized) {
      setIsInitialized(true);
      // 启动淡入动画
      Animated.timing(goalWrapperOpacity, {
        toValue: 1,
        duration: 300,
        useNativeDriver: true,
      }).start();
    }
  };

  // 三张卡片的拖动动画和zIndex
  const [topZ, setTopZ] = useState([1, 2, 3]); // bot, mid, top
  // bot
  const panBot = useRef(new Animated.ValueXY()).current;
  const zBot = topZ[0];
  const panResponderBot = useRef(
    PanResponder.create({
      onStartShouldSetPanResponder: () => true,
      onPanResponderGrant: () => { setTopZ([3, topZ[1], topZ[2]]); setScrollEnabled(false); }, // 禁用滚动
      onPanResponderMove: Animated.event([
        null,
        { dy: panBot.y }
      ], { useNativeDriver: false }),
      onPanResponderRelease: (_, gestureState) => {
        if (gestureState.dy < -120) {
          // 向上拖动超过阈值，跳转到考试冲刺页面
          router.push('/Train/ExamSprint');
        }
        Animated.spring(panBot, { toValue: { x: 0, y: 0 }, useNativeDriver: false }).start(() => setTopZ([1, 2, 3]));
        setScrollEnabled(true); // 恢复滚动
      },
    })
  ).current;
  // mid
  const panMid = useRef(new Animated.ValueXY()).current;
  const zMid = topZ[1];
  const panResponderMid = useRef(
    PanResponder.create({
      onStartShouldSetPanResponder: () => true,
      onPanResponderGrant: () => { setTopZ([topZ[0], 3, topZ[2]]); setScrollEnabled(false); }, // 禁用滚动
      onPanResponderMove: Animated.event([
        null,
        { dy: panMid.y }
      ], { useNativeDriver: false }),
      onPanResponderRelease: (_, gestureState) => {
        if (gestureState.dy < -120) {
          router.push('/training-detail');
        }
        Animated.spring(panMid, { toValue: { x: 0, y: 0 }, useNativeDriver: false }).start(() => setTopZ([1, 2, 3]));
        setScrollEnabled(true); // 恢复滚动
      },
    })
  ).current;
  // top
  const panTop = useRef(new Animated.ValueXY()).current;
  const zTop = topZ[2];
  const panResponderTop = useRef(
    PanResponder.create({
      onStartShouldSetPanResponder: () => true,
      onPanResponderGrant: () => { setTopZ([topZ[0], topZ[1], 3]); setScrollEnabled(false); }, // 禁用滚动
      onPanResponderMove: Animated.event([
        null,
        { dy: panTop.y }
      ], { useNativeDriver: false }),
      onPanResponderRelease: (_, gestureState) => {
        if (gestureState.dy < -120) {
          // 向上拖动超过阈值，跳转到归纳推荐页面
          router.push('/Train/InductiveRecommendation');
        }
        Animated.spring(panTop, { toValue: { x: 0, y: 0 }, useNativeDriver: false }).start(() => setTopZ([1, 2, 3]));
        setScrollEnabled(true); // 恢复滚动
      },
    })
  ).current;
  // 替换视频卡片图片数组
  const videoImages = [
    require('../../assets/Index_image/PhotoZero.png'),
    require('../../assets/Index_image/PhotoOne.jpg'),
    require('../../assets/Index_image/PhotoTwo.png'),
    require('../../assets/Index_image/PhotoThree.png'),
  ];
  // 引入新的服务按钮图片
  const serviceImages = [
    require('../../assets/Index_image/TextBook.png'),
    require('../../assets/Index_image/OnlineBook.png'),
    require('../../assets/Index_image/Knowledge.png'),
  ];
  // 引入底部功能栏图片
  const cardInfoImages = [
    require('../../assets/Index_image/TV.png'),
    require('../../assets/Talking.png'),
  ];

  // 获取课程数据（支持游客模式）
  useEffect(() => {
    setLoading(true);

    // 尝试获取用户ID，如果没有则使用游客模式
    const loadData = async () => {
      try {
        // 检查是否有认证token
        const token = await apiServices.auth.validateToken();
        let userId = null;

        if (token) {
          try {
            // 尝试获取用户信息
            const userInfo = await apiServices.auth.validateUserBasic();
            userId = userInfo?.id;
            console.log('用户模式，userId:', userId);
          } catch (error) {
            console.log('获取用户信息失败，使用游客模式:', error.message);
          }
        }

        console.log('数据加载模式:', userId ? '登录用户' : '游客模式');

        // 获取首页数据
        const res = await apiServices.content.getIndexData(userId);
        const data = res.data;
        console.log('首页数据解析:', data);

        setFeaturedCourses(data.featuredContentList || []);
        setHotCourses(data.hotCourseList || []);
        setLatestCourses(data.latestCourseList || []);

        // 处理推荐视频数据，确保正确解析 interestRecommend 和 profileRecommend
        const interestRecommend = data.interestRecommend || [];
        const profileRecommend = data.profileRecommend || [];
        const guestRecommend = data.guestRecommend || data.defaultRecommend || [];
        const oldRecommendData = data.recommendVideoList || [];

        let recommendVideos;
        if (!userId) {
          // 游客模式：优先使用游客推荐
          recommendVideos = guestRecommend.length > 0 ? guestRecommend :
            oldRecommendData.length > 0 ? oldRecommendData :
              interestRecommend.length > 0 ? interestRecommend : profileRecommend;
        } else {
          // 登录用户：优先使用个性化推荐
          recommendVideos = interestRecommend.length > 0 ? interestRecommend :
            profileRecommend.length > 0 ? profileRecommend :
              guestRecommend.length > 0 ? guestRecommend : oldRecommendData;
        }

        console.log('推荐视频数据解析:', {
          mode: userId ? '登录用户' : '游客模式',
          interestRecommend: interestRecommend.length,
          profileRecommend: profileRecommend.length,
          guestRecommend: guestRecommend.length,
          oldRecommendData: oldRecommendData.length,
          finalRecommendVideos: recommendVideos.length
        });

        setRecommendVideos(recommendVideos);

        setLoading(false);
      } catch (err) {
        console.error('首页获取失败:', err);
        setFeaturedCourses([]);
        setHotCourses([]);
        setLatestCourses([]);
        setRecommendVideos([]);
        setLoading(false);
      }
    };

    loadData();
  }, []);

  return (
    <WebCompatibleView
      style={styles.webContainer}
      fallbackStyle={styles.fallbackStyle}
      initialDelay={Platform.OS === 'web' ? 150 : 0}
    >
      <ThemedView style={styles.container}>
        {/* 安全视图 */}
        <View style={styles.safeArea} />

        <ScrollView contentContainerStyle={[styles.scrollContent, { paddingTop: 60 }]} showsVerticalScrollIndicator={false} scrollEnabled={scrollEnabled}>
          {/* 顶部Tab分页器 */}
          <View style={styles.tabRow}>
            {tabs.map((tab) => (
              <TouchableOpacity
                key={tab}
                style={styles.tabButton}
                onPress={() => setActiveTab(tab)}
              >
                <ThemedText style={[
                  styles.tabText,
                  activeTab === tab && styles.tabActive
                ]}>
                  {tab}
                </ThemedText>
              </TouchableOpacity>
            ))}
          </View>
          {/* 搜索框和吉祥物，仅在“视频”Tab显示 */}
          {activeTab === '视频' && (
            <View style={styles.searchRow}>
              <TouchableOpacity
                style={{ flex: 1, position: 'relative', justifyContent: 'center' }}
                onPress={wrapGuestAction(() => {
                  console.log('搜索功能');
                }, 'search')}
              >
                <Image source={searchIcon} style={{ width: 20, height: 20, position: 'absolute', left: 12, top: '50%', marginTop: -15, zIndex: 1 }} />
                <TextInput
                  placeholder="搜索"
                  style={[styles.searchInput, { paddingLeft: 38 }]}
                  placeholderTextColor="#bbb"
                  editable={false}
                  pointerEvents="none"
                />
              </TouchableOpacity>
              <Image source={mascot} style={styles.mascot} />
            </View>
          )}
          {/* 根据当前Tab渲染对应内容 */}
          {activeTab === '视频' && (
            <>
              {/* 我的学习目标板块 */}
              <ThemedCard style={[styles.goalCard, { padding: 0, overflow: 'hidden' }]}>
                <LinearGradient
                  colors={['#F3D4AD', '#FFEAD3']}
                  start={{ x: 0, y: 0 }}
                  end={{ x: 0, y: 1 }}
                  style={{ padding: 16, borderRadius: 16 }}
                >
                  <View style={{ flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center' }}>
                    <View>
                      <ThemedText style={styles.goalTitle}>我的学习目标</ThemedText>
                      <ThemedText style={styles.goalDesc}>打卡代表你完成了相关目标的学习哦~</ThemedText>
                    </View>
                    <TouchableOpacity><ThemedText style={styles.goalAdjust}>目标调整 {'>'}</ThemedText></TouchableOpacity>
                  </View>
                  {/* 目标Tab分页器和内容区域整体容器 */}
                  <View style={styles.goalTabWrapper}>
                    {/* 目标Tab分页器 */}
                    <View style={styles.goalTabContainer}>
                      <TouchableOpacity
                        style={[styles.goalTabBox, activeGoalTab === '考试' && styles.goalTabBoxActive]}
                        onPress={() => setActiveGoalTab('考试')}
                      >
                        <ThemedText style={[styles.goalTabText, activeGoalTab === '考试' && styles.goalTabTextActive]}>考试</ThemedText>
                      </TouchableOpacity>
                      <TouchableOpacity
                        style={[styles.goalTabBox, activeGoalTab === '英语四六级' && styles.goalTabBoxActive]}
                        onPress={() => setActiveGoalTab('英语四六级')}
                      >
                        <ThemedText style={[styles.goalTabText, activeGoalTab === '英语四六级' && styles.goalTabTextActive]}>英语四六级</ThemedText>
                      </TouchableOpacity>
                    </View>
                    {/* 内层卡片：根据Tab切换内容 */}
                    {activeGoalTab === '考试' && (
                      <View style={[styles.goalContentContainer, styles.goalContentSelected]}>
                        <Animated.View style={{ opacity: fadeAnimation }}>
                          <View style={styles.goalFormContainer}>
                            <ThemedText style={styles.goalCountdownText}>距离考研还有</ThemedText>
                            <ThemedText style={styles.goalCountdownNum}>13</ThemedText>
                            <View style={styles.goalBottomRow}>
                              <ThemedText style={styles.goalCountdownSub}>目标日：2025-11-19 星期三</ThemedText>
                              <TouchableOpacity style={styles.goalPunchBtn}>
                                <ThemedText style={styles.goalPunchBtnText}>学习打卡</ThemedText>
                              </TouchableOpacity>
                            </View>
                          </View>
                        </Animated.View>
                      </View>
                    )}
                    {activeGoalTab === '英语四六级' && (
                      <View style={styles.goalContentContainer}>
                        <Animated.View style={{ opacity: fadeAnimation }}>
                          <View style={styles.goalFormContainer}>
                            <ThemedText style={styles.goalCountdownText}>距离英语四六级还有</ThemedText>
                            <ThemedText style={styles.goalCountdownNum}>45</ThemedText>
                            <View style={styles.goalBottomRow}>
                              <ThemedText style={styles.goalCountdownSub}>目标日：2025-12-15 星期日</ThemedText>
                              <TouchableOpacity style={styles.goalPunchBtnInactive}>
                                <ThemedText style={styles.goalPunchBtnTextInactive}>已学习打卡</ThemedText>
                              </TouchableOpacity>
                            </View>
                          </View>
                        </Animated.View>
                      </View>
                    )}
                  </View>
                </LinearGradient>
              </ThemedCard>
              {/* 今日标题 */}
              <ThemedText style={styles.todayTitle}>今日</ThemedText>
              {/* 今日统计卡片 */}
              <LinearGradient
                colors={['#E5F5FF', '#E5F5FF', '#FFD29B']}
                locations={[0, 0.2, 1]}
                start={{ x: 0.5, y: 0 }}
                end={{ x: 0.5, y: 1 }}
                style={styles.todayCard}
              >
                <TouchableOpacity style={styles.todayItem} onPress={() => router.push('/index_today_task')}>
                  <View style={styles.todayItemRow}>
                    <ThemedText style={styles.todayItemTitle}>今日任务</ThemedText>
                    <ThemedText style={styles.todayItemArrow}>{'>'}</ThemedText>
                  </View>
                  <ThemedText style={styles.todayItemValue}><ThemedText style={styles.todayItemValueNum}>0</ThemedText>/3 个</ThemedText>
                </TouchableOpacity>
                <View style={styles.todayDivider} />
                <View style={styles.todayItem}>
                  <ThemedText style={styles.todayItemTitle}>今日学习</ThemedText>
                  <ThemedText style={styles.todayItemValue}><ThemedText style={styles.todayItemValueNum}>70</ThemedText>分钟</ThemedText>
                </View>
                <View style={styles.todayDivider} />
                <TouchableOpacity style={styles.todayItem} onPress={() => router.push('/learning-calendar')}>
                  <View style={styles.todayItemRow}>
                    <ThemedText style={styles.todayItemTitle}>学习日历</ThemedText>
                    <ThemedText style={styles.todayItemArrow}>{'>'}</ThemedText>
                  </View>
                  <ThemedText style={styles.todayItemValue}><ThemedText style={styles.todayItemValueNum}>3</ThemedText>天签到</ThemedText>
                </TouchableOpacity>
              </LinearGradient>
              {/* 服务按钮区 */}
              <View style={styles.serviceRow}>
                {services.map((s, idx) => (
                  <TouchableOpacity
                    key={s.label}
                    style={[styles.serviceBtn, idx !== services.length - 1 && { marginRight: 12 }]}
                    onPress={wrapGuestAction(() => {
                      if (s.label === '同步教材') {
                        router.push('/Today/SynchronizedText');
                      } else if (s.label === '网课学习') {
                        console.log('网课学习功能');
                      } else if (s.label === '知识归纳') {
                        console.log('知识归纳功能');
                      }
                    }, 'learning-tools')}
                  >
                    <ThemedText style={styles.serviceBtnText}>{s.label}</ThemedText>
                    <Image source={serviceImages[idx]} style={styles.serviceBtnImg} />
                  </TouchableOpacity>
                ))}
              </View>
              {/* 视频标题和查看更多 */}
              <View style={styles.sectionHeaderRow}>
                <ThemedText style={styles.sectionTitle}>视频</ThemedText>
                <TouchableOpacity><ThemedText style={styles.sectionMore}>查看更多</ThemedText></TouchableOpacity>
              </View>
              {/* 视频内容卡片区 */}
              <View style={styles.cardGrid}>
                {loading ? (
                  <View style={{ width: '100%', padding: 20, alignItems: 'center' }}>
                    <ThemedText style={{ color: '#888' }}>加载中...</ThemedText>
                  </View>
                ) : recommendVideos.length > 0 ? (
                  recommendVideos.slice(0, 4).map((video, index) => (
                    <TouchableOpacity
                      key={video.id || index}
                      onPress={wrapGuestAction(() => {
                        console.log('推荐视频点击:', video);
                        // 这里可以添加跳转逻辑
                        if (index === 0) {
                          router.push('/Today/EnglishClass');
                        }
                      }, 'video-interaction')}
                      style={{ width: '48%', marginBottom: 12 }}
                    >
                      <ThemedCard style={styles.contentCard}>
                        <View style={styles.cardImgWrap}>
                          <CachedImage
                            source={video.coverUrl}
                            style={[styles.cardImg, { resizeMode: 'cover' }]}
                            placeholder="https://via.placeholder.com/300x200?text=No+Image"
                            fadeInDuration={200}
                            enablePreload={true}
                          />
                          {/* 文字信息叠加在图片上 */}
                          <LinearGradient
                            colors={['rgba(0,0,0,0)', 'rgba(0,0,0,0.1)', 'rgba(0,0,0,0.3)']}
                            locations={[0, 0.6, 1]}
                            start={{ x: 0, y: 0 }}
                            end={{ x: 0, y: 1 }}
                            style={styles.cardOverlay}
                          >
                            <ThemedText style={styles.cardTitle}>
                              {video.contentTitle || '无标题'}
                            </ThemedText>
                            <View style={styles.cardSubRow}>
                              <ThemedText style={styles.cardSub}>
                                {video.sourceUniversity || '未知来源'}
                              </ThemedText>
                              <ThemedText style={styles.cardTime}>
                                {video.durationSeconds ? `${Math.floor(video.durationSeconds / 60)}:${(video.durationSeconds % 60).toString().padStart(2, '0')}` : '00:00'}
                              </ThemedText>
                            </View>
                          </LinearGradient>
                        </View>
                        {/* 底部功能栏 */}
                        <View style={styles.cardInfoWrap}>
                          <View style={styles.cardInfoRow}>
                            <Image source={cardInfoImages[0]} style={{ width: 18, height: 18, marginRight: 0 }} />
                            <ThemedText style={styles.cardInfo}>{formatNumber(video.playCount)}</ThemedText>
                            <Image source={cardInfoImages[1]} style={{ width: 18, height: 18, marginLeft: 56, marginRight: 0 }} />
                            <ThemedText style={styles.cardInfo}>{formatNumber(video.commentCount || 0)}</ThemedText>
                          </View>
                        </View>
                      </ThemedCard>
                    </TouchableOpacity>
                  ))
                ) : (
                  <View style={{ width: '100%', padding: 20, alignItems: 'center' }}>
                    <ThemedText style={{ color: '#888' }}>暂无推荐视频</ThemedText>
                  </View>
                )}
              </View>

              {/* 本周课程排行榜区 */}
              < View style={styles.sectionMargin} />
              <ThemedText style={styles.sectionTitle}>本周课程排行榜</ThemedText>
              <ScrollView
                horizontal
                showsHorizontalScrollIndicator={false}
                style={styles.rankScrollView}
                contentContainerStyle={styles.rankScrollContent}
              >
                {/* 热门排行卡片 */}
                <ThemedCard style={[styles.rankCard, styles.rankCardHorizontal]}>
                  <LinearGradient
                    colors={['rgba(255, 186, 101, 1)', 'rgba(255,210,155,1)', 'rgba(255,210,155,0)']}
                    locations={[0, 0.15, 1]}
                    start={{ x: 0.5, y: 0 }}
                    end={{ x: 0.5, y: 1 }}
                    style={styles.rankGradient}
                  >
                    <View style={styles.rankHeader}>
                      <ThemedText style={styles.rankTitle}>热门排行</ThemedText>
                      <ThemedText style={styles.rankSubtitle}>TOP3</ThemedText>
                      <TouchableOpacity style={styles.rankMoreBtn}>
                        <ThemedText style={styles.rankMoreText}>更多 {'>'}</ThemedText>
                      </TouchableOpacity>
                    </View>
                    <View style={styles.rankList}>
                      {loading ? (
                        <View style={{ padding: 20, alignItems: 'center' }}>
                          <ThemedText style={{ color: '#888', fontSize: 12 }}>加载中...</ThemedText>
                        </View>
                      ) : hotCourses.length > 0 ? (
                        hotCourses.slice(0, 3).map((course, index) => (
                          <TouchableOpacity
                            key={course.id || index}
                            style={styles.rankItem}
                            onPress={() => {
                              console.log('热门课程点击:', course);
                              // 这里可以添加跳转逻辑
                            }}
                          >
                            <CachedImage
                              source={course.coverUrl}
                              style={styles.rankItemImage}
                              placeholder="https://via.placeholder.com/60x60?text=No+Image"
                              fadeInDuration={150}
                              enablePreload={true}
                            />
                            <View style={styles.rankItemContent}>
                              <ThemedText style={styles.rankItemText}>{course.contentTitle || '无标题'}</ThemedText>
                              <ThemedText style={styles.rankItemSub}>{course.sourceUniversity || '未知来源'}</ThemedText>
                            </View>
                          </TouchableOpacity>
                        ))
                      ) : (
                        <View style={{ padding: 20, alignItems: 'center' }}>
                          <ThemedText style={{ color: '#888', fontSize: 12 }}>暂无热门课程</ThemedText>
                        </View>
                      )}
                    </View>
                  </LinearGradient>
                </ThemedCard>

                {/* 新课程卡片 */}
                <ThemedCard style={[styles.rankCard, styles.rankCardHorizontal]}>
                  <LinearGradient
                    colors={['rgba(255, 186, 101, 1)', 'rgba(255,210,155,1)', 'rgba(255,210,155,0)']}
                    locations={[0, 0.15, 1]}
                    start={{ x: 0.5, y: 0 }}
                    end={{ x: 0.5, y: 1 }}
                    style={styles.rankGradient}
                  >
                    <View style={styles.rankHeader}>
                      <ThemedText style={styles.rankTitle}>新课程</ThemedText>
                      <ThemedText style={styles.rankSubtitle}>最新上线</ThemedText>
                      <TouchableOpacity style={styles.rankMoreBtn}>
                        <ThemedText style={styles.rankMoreText}>更多 {'>'}</ThemedText>
                      </TouchableOpacity>
                    </View>
                    <View style={styles.rankList}>
                      {loading ? (
                        <View style={{ padding: 20, alignItems: 'center' }}>
                          <ThemedText style={{ color: '#888', fontSize: 12 }}>加载中...</ThemedText>
                        </View>
                      ) : latestCourses.length > 0 ? (
                        latestCourses.slice(0, 3).map((course, index) => (
                          <TouchableOpacity
                            key={course.id || index}
                            style={styles.rankItem}
                            onPress={() => {
                              console.log('最新课程点击:', course);
                              // 这里可以添加跳转逻辑
                            }}
                          >
                            <CachedImage
                              source={course.coverUrl}
                              style={styles.rankItemImage}
                              placeholder="https://via.placeholder.com/60x60?text=No+Image"
                              fadeInDuration={150}
                              enablePreload={true}
                            />
                            <View style={styles.rankItemContent}>
                              <ThemedText style={styles.rankItemText}>{course.contentTitle || '无标题'}</ThemedText>
                              <ThemedText style={styles.rankItemSub}>{course.sourceUniversity || '未知来源'}</ThemedText>
                            </View>
                          </TouchableOpacity>
                        ))
                      ) : (
                        <View style={{ padding: 20, alignItems: 'center' }}>
                          <ThemedText style={{ color: '#888', fontSize: 12 }}>暂无新课程</ThemedText>
                        </View>
                      )}
                    </View>
                  </LinearGradient>
                </ThemedCard>
              </ScrollView>
              {/* 精品课程区 */}
              <ThemedText style={styles.sectionTitle}>精品课程</ThemedText>
              {
                loading ? (
                  <View style={{ padding: 20, alignItems: 'center' }}>
                    <ThemedText style={{ color: '#888' }}>加载中...</ThemedText>
                  </View>
                ) : featuredCourses.length > 0 ? (
                  featuredCourses.map((course, index) => (
                    <TouchableOpacity
                      key={course.id || index}
                      style={{ borderRadius: 16, overflow: 'hidden', backgroundColor: '#fff', marginBottom: 16 }}
                      onPress={() => {
                        console.log('精品课程点击:', course);
                        // 这里可以添加跳转逻辑
                      }}
                    >
                      <CachedImage
                        source={course.coverUrl}
                        style={{ width: '100%', height: 120, borderTopLeftRadius: 16, borderTopRightRadius: 16, resizeMode: 'cover' }}
                        placeholder="https://via.placeholder.com/400x120?text=No+Image"
                        fadeInDuration={200}
                        enablePreload={true}
                      />
                      <View style={{ flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between', padding: 16, backgroundColor: '#fff', borderBottomLeftRadius: 16, borderBottomRightRadius: 16 }}>
                        <View style={{ flex: 1 }}>
                          <ThemedText style={{ fontSize: 16, fontWeight: 'bold', color: '#222' }}>{course.contentTitle || '无标题'}</ThemedText>
                          <ThemedText style={{ fontSize: 13, color: '#888', marginTop: 2 }}>{course.sourceUniversity || '未知来源'}</ThemedText>
                        </View>
                        <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                          <ThemedText style={{ fontSize: 14, color: '#888', marginRight: 2 }}>课程介绍</ThemedText>
                          <ThemedText style={{ fontSize: 16, color: '#bbb' }}>{'>'}</ThemedText>
                        </View>
                      </View>
                    </TouchableOpacity>
                  ))
                ) : (
                  <View style={{ padding: 20, alignItems: 'center' }}>
                    <ThemedText style={{ color: '#888' }}>暂无精品课程</ThemedText>
                  </View>
                )
              }
            </>
          )
          }
          {/* 训练Tab内容 */}
          {
            activeTab === '训练' && (
              <View style={styles.trainingContainer}>
                {/* 训练模式头部素材和信息 */}
                <View style={styles.trainingHeaderRow}>
                  <Image source={require('../../assets/standdog.png')} style={styles.trainingHeaderDog} resizeMode="contain" />
                  <View style={styles.trainingHeaderTextWrap}>
                    <ThemedText style={styles.trainingHeaderTitle}>近期薄弱知识点：一元二次方程</ThemedText>
                    <ThemedText style={styles.trainingHeaderSub}>训练时长统计：<ThemedText style={styles.trainingHeaderSubNum}>881h</ThemedText></ThemedText>
                  </View>
                  <Image source={require('../../assets/Vector.png')} style={styles.trainingHeaderVector} />
                </View>
                {/* 三张叠加的卡片 */}
                <View style={{ minHeight: 420, marginTop: 16 }}>
                  <Animated.View
                    style={[
                      styles.trainingStackCard,
                      styles.trainingStackCardBot,
                      { zIndex: 3, transform: panBot.getTranslateTransform() }
                    ]}
                    {...panResponderBot.panHandlers}
                  >
                    <View style={{ marginTop: -140, width: '100%', alignItems: 'center' }}>
                      <ThemedText style={styles.trainingStackText}>考试冲刺</ThemedText>
                      <ThemedText style={styles.trainingStackSubText}>依据考试的内容给出有针对性的学习方案</ThemedText>
                    </View>
                  </Animated.View>
                  <Animated.View
                    style={[
                      styles.trainingStackCard,
                      styles.trainingStackCardMid,
                      { zIndex: 2, transform: panMid.getTranslateTransform() }
                    ]}
                    {...panResponderMid.panHandlers}
                  >
                    <View style={{ marginTop: -140, width: '100%', alignItems: 'center' }}>
                      <ThemedText style={styles.trainingStackText}>自主选择</ThemedText>
                      <ThemedText style={styles.trainingStackSubText}>依据关键词,难度,将简单分类进行选择</ThemedText>
                    </View>
                  </Animated.View>
                  <Animated.View
                    style={[
                      styles.trainingStackCard,
                      styles.trainingStackCardTop,
                      { zIndex: 1, transform: panTop.getTranslateTransform() }
                    ]}
                    {...panResponderTop.panHandlers}
                  >
                    <View style={{ marginTop: -140, width: '100%', alignItems: 'center' }}>
                      <ThemedText style={styles.trainingStackText}>归纳推荐</ThemedText>
                      <ThemedText style={styles.trainingStackSubText}>依据视频学习的内容以及错题，总结归纳</ThemedText>
                    </View>
                  </Animated.View>
                </View>
              </View>
            )
          }
          {/* 挑战Tab内容 */}
          {
            activeTab === '挑战' && (
              <View style={styles.challengeContainer}>
                {/* 挑战模式头部素材和信息 */}
                <View style={styles.trainingHeaderRow}>
                  <Image source={require('../../assets/standdog.png')} style={styles.trainingHeaderDog} resizeMode="contain" />
                  <View style={styles.trainingHeaderTextWrap}>
                    <ThemedText style={styles.trainingHeaderTitle}>近期挑战目标：单词拼写大赛</ThemedText>
                    <ThemedText style={styles.trainingHeaderSub}>挑战时长统计：<ThemedText style={styles.trainingHeaderSubNum}>12h 45min</ThemedText></ThemedText>
                  </View>
                  <Image source={require('../../assets/Vector.png')} style={styles.trainingHeaderVector} />
                </View>

                {/* 类型板块 */}
                <View style={styles.sectionRow}>
                  <ThemedText style={styles.sectionTitle}>类型</ThemedText>
                  <TouchableOpacity><Image source={require('../../assets/FrameOne.png')} style={styles.hotArrowImg} /></TouchableOpacity>
                </View>
                <ScrollView horizontal showsHorizontalScrollIndicator={false} style={{ marginBottom: 16 }}>
                  <TouchableOpacity
                    style={[styles.typeCard, { backgroundColor: Colors.primary, position: 'relative', overflow: 'hidden' }]}
                    onPress={() => {
                      console.log('学科闯关 clicked');
                      // 这里可以添加跳转逻辑
                    }}
                  >
                    <Image source={require('../../assets/dog.png')} style={styles.typeCardDogImg} />
                    <ThemedText style={[styles.typeCardDogText, { color: '#fff' }]}>学科闯关</ThemedText>
                  </TouchableOpacity>
                  <TouchableOpacity
                    style={[styles.typeCard, { backgroundColor: Colors.light.uiBackground, position: 'relative', overflow: 'hidden' }]}
                    onPress={() => {
                      console.log('积分排名 clicked');
                      router.push('/Challenge/KnowledgeRanking');
                    }}
                  >
                    <Image source={require('../../assets/dog.png')} style={styles.typeCardDogImg} />
                    <ThemedText style={[styles.typeCardDogText, { color: Colors.light.title }]}>积分排名</ThemedText>
                  </TouchableOpacity>
                  <TouchableOpacity
                    style={[styles.typeCard, { backgroundColor: '#fff', position: 'relative', overflow: 'hidden' }]}
                    onPress={() => {
                      console.log('挑战比赛 clicked');
                      // 这里可以添加跳转逻辑
                    }}
                  >
                    <Image source={require('../../assets/dog.png')} style={styles.typeCardDogImg} />
                    <ThemedText style={styles.typeCardDogText}>知识问答</ThemedText>
                  </TouchableOpacity>
                </ScrollView>

                {/* 今日热门板块 */}
                <View style={styles.sectionRow}>
                  <ThemedText style={styles.sectionTitle}>今日热门</ThemedText>
                  <TouchableOpacity><Image source={require('../../assets/FrameOne.png')} style={styles.hotArrowImg} /></TouchableOpacity>
                </View>
                <View style={styles.hotRow}>
                  <TouchableOpacity style={styles.hotArrowWrap}>
                    <Image source={require('../../assets/Frame.png')} style={styles.hotArrowImg} />
                  </TouchableOpacity>
                  <View style={styles.hotCard}>
                    <ThemedText style={styles.hotCardTag}>学科闯关</ThemedText>
                  </View>
                </View>
                <View style={styles.hotRow}>
                  <View style={styles.hotCard}>
                    <ThemedText style={styles.hotCardTag}>互动阅读冒险</ThemedText>
                  </View>
                  <TouchableOpacity style={styles.hotArrowWrap}>
                    <Image source={require('../../assets/Frame.png')} style={[styles.hotArrowImg, { transform: [{ rotate: '180deg' }] }]} />
                  </TouchableOpacity>
                </View>
                <View style={styles.hotRow}>
                  <TouchableOpacity style={styles.hotArrowWrap}>
                    <Image source={require('../../assets/Frame.png')} style={styles.hotArrowImg} />
                  </TouchableOpacity>
                  <View style={styles.hotCard}>
                    <ThemedText style={styles.hotCardTag}>知识问答</ThemedText>
                  </View>
                </View>
              </View>
            )
          }
        </ScrollView >
      </ThemedView >

      {/* 游客权限提示弹窗 */}
      <GuestPermissionModal
        visible={showPermissionModal}
        onClose={closePermissionModal}
        onRegister={handleRegister}
        featureName={currentFeature.name}
        description={currentFeature.description}
      />
    </WebCompatibleView>
  )
}

export default Home

const styles = StyleSheet.create({
  webContainer: {
    flex: 1,
    backgroundColor: '#FFF8F3',
  },
  fallbackStyle: {
    backgroundColor: '#FFF8F3',
    justifyContent: 'center',
    alignItems: 'center',
  },
  container: {
    flex: 1,
    backgroundColor: '#FFF8F3',
    paddingHorizontal: Platform.OS === 'web' ? 20 : 16
  },
  safeArea: {
    height: 50,
    backgroundColor: '#FFF8F3',
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    zIndex: 15,
  },
  scrollContent: { paddingBottom: 120 },
  tabRow: { flexDirection: 'row', marginTop: 0, marginBottom: 10, justifyContent: 'space-around' },
  tabButton: { flex: 1, alignItems: 'center' },
  tabText: { fontSize: 18, fontWeight: 'bold', color: Colors.light.iconColor, paddingBottom: 8, paddingTop: 10 },
  tabActive: { color: Colors.light.iconColorFocused, borderBottomWidth: 3, borderColor: Colors.light.iconColorFocused },
  searchRow: { flexDirection: 'row', alignItems: 'center', marginBottom: 10 },
  searchInput: { backgroundColor: '#FFE7CD', borderRadius: 20, paddingHorizontal: 16, height: 36, fontSize: 14 },
  mascot: { width: 48, height: 48, marginLeft: 10 },
  todayTitle: { fontSize: 16, fontWeight: 'bold', color: Colors.light.title, marginTop: 0, marginBottom: 16 },
  todayCard: { flexDirection: 'row', borderRadius: 18, padding: 16, marginBottom: 12, alignItems: 'center', justifyContent: 'space-between' },
  todayItem: { flex: 1, alignItems: 'center' },
  todayItemRow: { flexDirection: 'row', alignItems: 'center', justifyContent: 'center' },
  todayItemTitle: { fontSize: 15, color: Colors.light.text, marginRight: 2 },
  todayItemArrow: { fontSize: 15, color: Colors.light.iconColor },
  todayItemValue: { fontSize: 14, color: Colors.light.iconColor, marginTop: 6 },
  todayItemValueNum: { fontSize: 22, color: Colors.light.title, fontWeight: 'bold' },
  todayDivider: { width: 1, height: 36, backgroundColor: Colors.light.iconColor, marginHorizontal: 8 },
  serviceRow: { flexDirection: 'row', marginBottom: 10, justifyContent: 'space-between' },
  serviceBtn: { flex: 1, flexDirection: 'row', alignItems: 'center', backgroundColor: Colors.light.uiBackground, borderRadius: 12, paddingHorizontal: 18, paddingVertical: 16, overflow: 'hidden', minWidth: 0 },
  serviceBtnText: { color: Colors.light.iconColor, fontSize: 15, flex: 1 },
  serviceBtnImg: { width: 36, height: 36, resizeMode: 'contain', marginLeft: 6 },
  cardGrid: { flexDirection: 'row', flexWrap: 'wrap', justifyContent: 'space-between', paddingBottom: 0 },
  contentCard: { borderRadius: 16, padding: 0, overflow: 'hidden', backgroundColor: '#fff' },
  cardImgWrap: { width: '100%', aspectRatio: 1.2, backgroundColor: Colors.light.iconColor, position: 'relative', justifyContent: 'flex-end' },
  cardImg: { width: '100%', height: '100%', borderTopLeftRadius: 16, borderTopRightRadius: 16 },
  cardOverlay: { position: 'absolute', left: 0, right: 0, bottom: 0, height: '50%', padding: 12, justifyContent: 'center' },
  cardTitle: { fontSize: 14, fontWeight: 'medium', color: '#fff', marginBottom: 4 },
  cardSubRow: { flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center' },
  cardSub: { fontSize: 10, color: '#fff', marginBottom: 0, fontWeight: 'regular' },
  cardTime: { fontSize: 10, color: '#fff' },
  cardInfoWrap: { backgroundColor: '#fff', padding: 12, borderBottomLeftRadius: 16, borderBottomRightRadius: 16 },
  cardInfoRow: { flexDirection: 'row', justifyContent: 'space-between', marginBottom: 4 },
  cardInfo: { fontSize: 10, color: Colors.light.iconColor },
  cardFooter: { fontSize: 12, color: Colors.light.iconColor },
  sectionHeaderRow: { flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center', marginTop: 8, marginBottom: 4 },
  sectionTitle: { fontSize: 16, fontWeight: 'bold', color: Colors.light.title, marginTop: 14, marginBottom: 16 },
  sectionMore: { fontSize: 12, color: Colors.light.iconColor, marginRight: 8 },
  // 排行榜滚动视图样式
  rankScrollView: {
    marginBottom: 16,
  },
  rankScrollContent: {
    paddingHorizontal: 0,
    gap: 16,
  },
  // 排行榜卡片样式
  rankCard: {
    borderRadius: 16,
    padding: 0,
    marginTop: 0,
    marginBottom: 0,
    backgroundColor: Colors.light.background,
    overflow: 'hidden',
  },
  rankCardHorizontal: {
    width: 350, // 固定宽度，确保两个卡片并排显示
    marginRight: 16,
  },
  rankGradient: {
    padding: 16,
    borderRadius: 16,
  },
  // 排行榜头部样式
  rankHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  rankTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: Colors.light.title,
    marginRight: 8,
  },
  rankSubtitle: {
    fontSize: 12,
    color: Colors.light.iconColor,
    flex: 1,
  },
  rankMoreBtn: {
    paddingHorizontal: 8,
    paddingVertical: 4,
  },
  rankMoreText: {
    fontSize: 12,
    color: Colors.light.iconColor,
  },
  // 排行榜列表样式
  rankList: {
    gap: 8,
  },
  rankItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  rankItemImage: {
    width: 64,
    height: 36,
    borderRadius: 12,
    marginRight: 8,
  },
  rankItemContent: {
    flex: 1,
  },
  rankItemText: {
    fontSize: 14,
    color: Colors.light.title,
    marginBottom: 2,
  },
  rankItemSub: {
    fontSize: 12,
    color: '#994F16',
  },
  featuredCard: { borderRadius: 16, padding: 24, marginBottom: 24, backgroundColor: Colors.light.uiBackground, alignItems: 'center' },
  featuredText: { fontSize: 15, color: Colors.light.iconColor },
  goalCard: { borderRadius: 16, padding: 16, marginBottom: 16, backgroundColor: Colors.light.uiBackground },
  goalTitle: { fontSize: 16, fontWeight: 'bold', marginBottom: 2 },
  goalDesc: { fontSize: 12, color: Colors.light.iconColor },
  goalAdjust: { fontSize: 12, color: Colors.light.iconColor },
  // 目标Tab整体包装器
  goalTabWrapper: {
    marginTop: 16,
    marginHorizontal: -16,
    marginBottom: -16,
    backgroundColor: 'transparent',
    borderBottomLeftRadius: 16,
    borderBottomRightRadius: 16,
    overflow: 'hidden',
    // 添加固定高度避免初始渲染错位
    minHeight: 160,
    // 确保子元素紧密贴合
    flexDirection: 'column',
  },
  // 新的目标Tab分页器样式
  goalTabContainer: {
    backgroundColor: 'transparent',
    paddingTop: 0,
    paddingHorizontal: 0,
    paddingBottom: 0,
    flexDirection: 'row',
    marginTop: 0,
    marginBottom: 0,
    gap: 0,
    // 确保与内容区域无缝连接
    borderTopLeftRadius: 16,
    borderTopRightRadius: 16,
    zIndex: 1,
  },
  goalTabBox: {
    flex: 1,
    backgroundColor: '#FFD29B',
    borderTopLeftRadius: 12,
    borderTopRightRadius: 12,
    paddingVertical: 8,
    paddingHorizontal: 12,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 0,
    marginHorizontal: 0,
  },
  goalTabBoxActive: {
    backgroundColor: '#F59622',
    marginBottom: 0,
  },
  goalTabText: {
    fontSize: 14,
    color: '#666',
    fontWeight: '500',
  },
  goalTabTextActive: {
    color: '#fff',
    fontWeight: 'bold',
  },
  // 内容容器样式
  goalContentContainer: {
    backgroundColor: Colors.light.background,
    marginTop: -1,
    marginHorizontal: 0,
    paddingHorizontal: 28,
    paddingVertical: 20,
    paddingTop: 21,
    minHeight: 120,
    // 确保与tab无缝连接，圆角与选择盒子匹配
    borderTopLeftRadius: 0,
    borderTopRightRadius: 0,
    borderBottomLeftRadius: 12,
    borderBottomRightRadius: 12,
  },
  goalContentSelected: {
    backgroundColor: '#FFE7CD',
  },
  goalFormContainer: {
    alignItems: 'center',
  },
  goalCountdownText: {
    fontSize: 18,
    color: Colors.light.text,
    marginBottom: 8,
  },
  goalCountdownNum: {
    fontSize: 55,
    color: Colors.light.title,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  goalCountdownSub: {
    fontSize: 11,
    color: Colors.light.iconColor,
  },
  goalBottomRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: 8,
  },
  goalPunchBtn: {
    backgroundColor: '#D57704',
    borderRadius: 16,
    paddingHorizontal: 12,
    paddingVertical: 6,
    marginLeft: 12,
  },
  goalPunchBtnText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: '500',
  },
  goalPunchBtnInactive: {
    backgroundColor: '#909090',
    borderRadius: 16,
    paddingHorizontal: 12,
    paddingVertical: 6,
    marginLeft: 12,
  },
  goalPunchBtnTextInactive: {
    color: '#fff',
    fontSize: 12,
    fontWeight: '500',
  },
  sectionMargin: { height: 0 },
  // 新增训练和挑战页面的样式
  trainingContainer: { paddingTop: 20 },
  trainingHeaderRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: '#FFF8F3',
    borderRadius: 12,
    paddingHorizontal: 8,
    paddingVertical: 8,
    marginBottom: 12,
  },
  trainingHeaderDog: {
    width: 72,
    height: 72,
    marginRight: 8,
  },
  trainingHeaderTextWrap: {
    flex: 1,
    justifyContent: 'center',
    marginHorizontal: 8,
  },
  trainingHeaderTitle: {
    fontSize: 18,
    color: Colors.light.title,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  trainingHeaderSub: {
    fontSize: 16,
    color: Colors.light.iconColor,
  },
  trainingHeaderSubNum: {
    color: Colors.light.iconColor,
    fontWeight: 'normal',
  },
  trainingHeaderVector: {
    width: 64,
    height: 64,
    resizeMode: 'contain',
    marginLeft: 8,
  },
  trainingCard: { borderRadius: 16, padding: 24, marginBottom: 24, backgroundColor: Colors.light.uiBackground, alignItems: 'center' },
  trainingText: { fontSize: 18, color: Colors.light.title, fontWeight: 'bold', marginBottom: 8 },
  trainingSubText: { fontSize: 14, color: Colors.light.iconColor, textAlign: 'center' },
  challengeContainer: { paddingTop: 20 },
  challengeCard: { borderRadius: 16, padding: 24, marginBottom: 24, backgroundColor: Colors.light.uiBackground, alignItems: 'center' },
  challengeText: { fontSize: 18, color: Colors.light.title, fontWeight: 'bold', marginBottom: 8 },
  challengeSubText: { fontSize: 14, color: Colors.light.iconColor, textAlign: 'center' },
  trainingStackCard: {
    position: 'absolute',
    left: 0,
    right: 0,
    marginHorizontal: 12,
    borderRadius: 24,
    padding: 24,
    paddingTop: 8, // 原来是24，改为8，让内容整体上移
    paddingBottom: 32, // 增加底部padding
    alignItems: 'center',
    aspectRatio: 1.15,
    justifyContent: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.08,
    shadowRadius: 12,
    elevation: 4,
  },
  trainingStackCardTop: {
    top: 0,
    zIndex: 3,
    backgroundColor: '#B9C7E7', // 自主选择 - 背景色
  },
  trainingStackCardMid: {
    top: 100,
    zIndex: 2,
    backgroundColor: '#FFA58C', // 归纳推荐 - 橙色
  },
  trainingStackCardBot: {
    top: 200,
    zIndex: 1,
    backgroundColor: '#FFE2CD', // 考试冲刺 - 浅橙色
  },
  trainingStackText: {
    fontSize: 20,
    color: Colors.light.title,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  trainingStackSubText: {
    fontSize: 14,
    color: Colors.light.iconColor,
    textAlign: 'center',
  },
  sectionRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginTop: 18,
    marginBottom: 8,
    paddingHorizontal: 2,
  },
  typeCard: {
    width: 120,
    height: 90,
    borderRadius: 16,
    backgroundColor: Colors.light.uiBackground,
    marginRight: 16,
    alignItems: 'center',
    justifyContent: 'center',
  },
  typeCardText: {
    fontSize: 17,
    color: Colors.light.title,
    fontWeight: 'bold',
  },
  hotRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  hotArrowWrap: {
    width: 44,
    height: 44,
    borderRadius: 22,
    backgroundColor: Colors.light.uiBackground,
    alignItems: 'center',
    justifyContent: 'center',
    marginHorizontal: 6,
  },
  hotArrow: {
    fontSize: 28,
    color: Colors.light.iconColor,
    fontWeight: 'bold',
  },
  hotCard: {
    flex: 1,
    height: 120,
    borderRadius: 18,
    backgroundColor: Colors.light.uiBackground,
    marginHorizontal: 0,
    justifyContent: 'flex-end',
    alignItems: 'flex-end',
    padding: 12,
  },
  hotCardSub: {
    flex: 1,
    height: 90,
    borderRadius: 18,
    backgroundColor: Colors.light.background,
    marginRight: 8,
    justifyContent: 'flex-start',
    alignItems: 'flex-start',
    padding: 12,
  },
  hotCardTag: {
    fontSize: 15,
    color: Colors.light.title,
    fontWeight: 'bold',
  },
  typeCardDogImg: {
    width: 56,
    height: 56,
    resizeMode: 'contain',
    alignSelf: 'center',
    marginTop: 8,
  },
  typeCardDogText: {
    position: 'absolute',
    left: 0,
    right: 0,
    bottom: 10,
    textAlign: 'center',
    fontSize: 17,
    color: Colors.light.title,
    fontWeight: 'bold',
  },
  hotArrowImg: {
    width: 28,
    height: 28,
    resizeMode: 'contain',
  },
}) 