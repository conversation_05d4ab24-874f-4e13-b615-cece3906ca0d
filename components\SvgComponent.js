import React from 'react';
import Svg, { Path } from 'react-native-svg';

const SvgComponent = (props) => (
  <Svg
    width="100%"
    height={180}
    viewBox="0 0 720 893"
    preserveAspectRatio="xMidYMid meet"
    {...props}
  >
    <Path
      d="M10 224C10 189.7 13.3 155.3 20 122C26.7 88.7 36.7 56.7 50 26C63.3 -4.7 81.7 -13.3 103 -16C124.3 -18.7 147.3 -14.7 171 -4C194.7 6.7 220 22.7 246 35C272 47.3 299 56.7 327 63C355 69.3 384 73.3 414 75C444 76.7 475 76.7 506 75C537 73.3 568 70 599 64C630 58 661 50 691 40C721 30 751 18 779 4C785.7 -2.7 792 -9.3 797 -17C802 -24.7 805 -32.7 806 -41C807 -49.3 806 -57.7 803 -66C799 -74.3 793 -82.3 786 -90C779 -97.7 771 -105.3 762 -112C753 -118.7 743 -125.3 732 -131C721 -136.7 709 -141.3 697 -145C685 -148.7 672 -151.3 659 -153C646 -154.7 633 -155.3 620 -155C594 -155 568 -151 542 -143C516 -135 490 -123 464 -107C438 -91 412 -71 386 -47C360 -23 334 1 308 25C282 49 256 71 230 91C204 111"
      fill="#F9F199"
    />
  </Svg>
);

export default SvgComponent; 