import React from 'react';
import { SafeAreaView, View, Text, StyleSheet, ScrollView, TextInput, TouchableOpacity, Image } from 'react-native';
import ThemedText from '../../components/ThemedText';
import ThemedView from '../../components/ThemedView';
import { Colors } from '../../constants/Colors';
import { useRouter } from 'expo-router'; // 引入 useRouter

export default function ExamSprint() {
  const router = useRouter(); // 获取 router 实例

  return (
    <View style={styles.mainContainer}>
      {/* 安全视图 */}
      <View style={styles.safeArea} />

      <ScrollView style={styles.scrollView} contentContainerStyle={[styles.container, { paddingTop: 60 }]}>
        {/* Header */}
        <View style={styles.header}>
          <TouchableOpacity onPress={() => router.push('/(dashboard)')}>
            <Image
              source={require('../../assets/FrameTwo.png')}
              style={styles.backButton}
            />
          </TouchableOpacity>
          <View style={styles.headerTitleContainer}>
            <ThemedText style={styles.headerTitle}>考试冲刺</ThemedText>
          </View>
        </View>
        {/* 搜索框 */}

        {/* 智能模拟测试 */}
        <Text style={styles.sectionTitle}>智能模拟测试</Text>
        <TouchableOpacity style={styles.card} onPress={() => {
          console.log('四级卡片被点击');
          router.push('/Train/ExamSprint/ExamOne');
        }}>
          <View style={styles.cardRow}>
            <Text style={styles.cardTag}>四级</Text>
            <Text style={styles.cardTitle}>英语四级听力真题</Text>
          </View>
          <View style={styles.cardBtnRow}>
            <Btn text="真题" />
            <Btn text="听力" />
            <Btn text="专项突破" />
          </View>
          <Text style={styles.cardGroup}>有道四级教研组</Text>
        </TouchableOpacity>
        <TouchableOpacity style={styles.card} onPress={() => {
          console.log('六级卡片被点击');
          // 这里可以添加跳转逻辑，例如：
          // router.push('/Train/EnglishLevel6');
        }}>
          <View style={styles.cardRow}>
            <Text style={styles.cardTag}>六级</Text>
            <Text style={styles.cardTitle}>六级考前听力刷题</Text>
          </View>
          <View style={styles.cardBtnRow}>
            <Btn text="真题" />
            <Btn text="专项突破" />
            <Btn text="听力" />
          </View>
          <Text style={styles.cardGroup}>有道六级教研组</Text>
        </TouchableOpacity>
        <TouchableOpacity style={styles.card} onPress={() => {
          console.log('计算机二级卡片被点击');
          // 这里可以添加跳转逻辑，例如：
          // router.push('/Train/ComputerLevel2');
        }}>
          <View style={styles.cardRow}>
            <Text style={styles.cardTag}>计算机二级</Text>
            <Text style={styles.cardTitle}>计算机二级真题专练</Text>
          </View>
          <View style={styles.cardBtnRow}>
            <Btn text="常考题" />
            <Btn text="经典题" />
            <Btn text="每日一练" />
          </View>
          <Text style={styles.cardGroup}>小黑老师</Text>
        </TouchableOpacity>
        {/* 智能组卷卡片 */}
        <TouchableOpacity style={styles.card} onPress={() => {
          console.log('智能组卷卡片被点击');
          router.push('/Train/ExamSprint/IntelligentTestPaperComposition');
        }}>
          <View style={styles.cardRow}>
            <Text style={styles.cardTag}>智能组卷</Text>
            <View style={styles.cardTitleRow}>
              <Text style={styles.cardTitle}>智能组卷系统</Text>
              <TouchableOpacity style={styles.addButton} onPress={(e) => {
                e.stopPropagation(); // 阻止事件冒泡
                console.log('加号按钮被点击');
                // 这里可以添加加号按钮的特定逻辑
              }}>
                <Text style={styles.addButtonText}>+</Text>
              </TouchableOpacity>
            </View>
          </View>
          <Text style={styles.cardGroup}>智能推荐</Text>
        </TouchableOpacity>
      </ScrollView >
    </View >
  );
}

function Btn({ text }) {
  return (
    <View style={styles.btn}><Text style={styles.btnText}>{text}</Text></View>
  );
}


const styles = StyleSheet.create({
  mainContainer: {
    flex: 1,
    backgroundColor: '#FFF8F3',
  },
  safeArea: {
    height: 50,
    backgroundColor: '#FFF8F3',
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    zIndex: 15,
  },
  scrollView: {
    flex: 1,
    backgroundColor: '#FFF8F3',
  },
  container: {
    backgroundColor: '#FFF8F3',
    paddingHorizontal: 16,
    paddingTop: 0,
    paddingBottom: 24,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
    paddingHorizontal: 0,
    paddingTop: 10,
  },
  backButton: {
    width: 24,
    height: 24,
    marginRight: 0,
  },
  headerTitleContainer: {
    flex: 1,
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#222',
  },
  searchBox: {
    backgroundColor: '#EFEFEF',
    borderRadius: 18,
    paddingHorizontal: 16,
    paddingVertical: 8,
    marginBottom: 18,
  },
  searchInput: {
    fontSize: 16,
    color: '#222',
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#222',
    marginBottom: 16,
    marginTop: 8,
  },
  card: {
    backgroundColor: '#fff',
    borderRadius: 18,
    padding: 16,
    marginBottom: 14,
    shadowColor: '#000',
    shadowOpacity: 0.04,
    shadowRadius: 4,
    elevation: 2,
  },
  cardRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  cardTag: {
    backgroundColor: '#F59622',
    color: '#FFF',
    borderRadius: 6,
    paddingHorizontal: 8,
    paddingVertical: 2,
    fontSize: 14,
    marginRight: 8,
    fontWeight: 'medium',
  },
  cardTitle: {
    fontSize: 16,
    color: '#222',
    fontWeight: 'bold',
  },
  cardTitleRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    flex: 1,
  },
  cardBtnRow: {
    flexDirection: 'row',
    marginBottom: 8,
  },
  btn: {
    backgroundColor: 'transparent',
    borderWidth: 1,
    borderColor: '#D1D5DB',
    borderRadius: 5,
    paddingHorizontal: 10,
    paddingTop: 0,

    paddingBottom: 0,
    marginRight: 6,
  },
  btnText: {
    fontSize: 10,
    color: '#333333',
  },
  cardGroup: {
    fontSize: 13,
    color: '#aaa',
    marginTop: 20,
  },
  addButton: {
    backgroundColor: 'transparent',
    paddingHorizontal: 8,
    paddingVertical: 4,
    alignItems: 'center',
    justifyContent: 'center',
    minWidth: 32,
  },
  addButtonText: {
    color: '#6B7280',
    fontSize: 12,
    fontWeight: '300',
  },


});