import React from 'react';
import { TouchableOpacity, Text, StyleSheet, View } from 'react-native';
import { BlurView } from 'expo-blur';
import { LinearGradient } from 'expo-linear-gradient';

const LiquidGlassButton = ({ title, onPress, style, textStyle, children }) => {
  return (
    <TouchableOpacity onPress={onPress} activeOpacity={0.8} style={[styles.buttonWrapper, style]}>
      <LinearGradient
        colors={['rgba(255, 255, 255, 0.08)', 'rgba(255, 255, 255, 0.01)']}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
        style={styles.gradientBorder}
      >
        <BlurView intensity={15} tint="light" style={styles.blurContainer}>
          {children || <Text style={[styles.text, textStyle]}>{title}</Text>}
        </BlurView>
      </LinearGradient>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  buttonWrapper: {
    borderRadius: 20,
    overflow: 'hidden',
    width: 200,
    height: 60,
  },
  gradientBorder: {
    flex: 1,
    padding: 2,
    borderRadius: 20,
  },
  blurContainer: {
    flex: 1,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.03)',
  },
  text: {
    color: '#fff',
    fontWeight: '600',
    fontSize: 18,
  },
});

export default LiquidGlassButton;
