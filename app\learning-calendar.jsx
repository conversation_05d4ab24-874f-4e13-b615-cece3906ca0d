import React, { useState, useRef, useEffect } from 'react';
import { StyleSheet, View, TouchableOpacity, Image, ScrollView, Animated, Platform } from 'react-native';
import { useRouter } from 'expo-router';
import ThemedText from "../components/ThemedText";
import { Colors } from "../constants/Colors";
import LiquidGlassButton from "../components/LiquidGlassButton";
import { shadowPresets } from "../utils/shadowUtils";

const images = [
  require('../assets/Index_image/background.png'),
  require('../assets/Index_image/PhotoFour.png'),
  require('../assets/Index_image/PhotoFive.png'),
  require('../assets/Index_image/PhotoSix.png'),
];

const overlayTexts = [
  [
    'Dont you want to take a leap of faith?',
    '你难道不想放手一博吗? ——《盗梦空间》'
  ],
  [
    'Whatever you do, do it a hundredpercent.',
    '倾尽所能,力求极致。——《绿皮书》'
  ],
  [
    'The higher I got, the more amazed.',
    '站着越高,风景越迷人。——《怦然心动》'
  ],
  [
    "Words and ideas can change the world.",
    '一知一言皆有憾世之力。——《死亡诗社》'
  ],
];

const LearningCalendar = () => {
  const router = useRouter();
  const [currentMonth] = useState('2025年7月');
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [nextImageIndex, setNextImageIndex] = useState(1);
  const fadeAnim = useRef(new Animated.Value(1)).current;
  const nextFadeAnim = useRef(new Animated.Value(0)).current;
  const buttonScale = useRef(new Animated.Value(1)).current;

  const handleChangeImage = () => {
    // 按钮点击反馈动画
    Animated.sequence([
      Animated.timing(buttonScale, {
        toValue: 0.95,
        duration: 100,
        useNativeDriver: Platform.OS !== 'web',
      }),
      Animated.timing(buttonScale, {
        toValue: 1,
        duration: 100,
        useNativeDriver: Platform.OS !== 'web',
      })
    ]).start();

    const nextIndex = (currentImageIndex + 1) % images.length;
    setNextImageIndex(nextIndex);

    // 使用简单的线性动画
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 0,
        duration: 400,
        useNativeDriver: Platform.OS !== 'web',
      }),
      Animated.timing(nextFadeAnim, {
        toValue: 1,
        duration: 400,
        useNativeDriver: Platform.OS !== 'web',
      })
    ]).start(() => {
      setCurrentImageIndex(nextIndex);
      fadeAnim.setValue(1);
      nextFadeAnim.setValue(0);
    });
  };

  return (
    <View style={styles.container}>
      {/* 安全视图 */}
      <View style={styles.safeArea} />

      <ScrollView style={styles.content} contentContainerStyle={{ paddingTop: 60 }} showsVerticalScrollIndicator={false}>
        {/* 顶部标题栏 */}
        <View style={styles.header}>
          <TouchableOpacity onPress={() => router.back()}>
            <Image source={require('../assets/FrameTwo.png')} style={[styles.backIcon, { width: 22, height: 22 }]} />
          </TouchableOpacity>
          <ThemedText style={styles.headerTitle}>学习日历</ThemedText>
          <View style={styles.headerRight} />
        </View>
        {/* 主卡片区域 */}
        <View style={styles.mainCard}>
          <Animated.Image
            source={images[currentImageIndex]}
            style={[styles.cardBackgroundImage, { opacity: fadeAnim, resizeMode: 'cover' }]}
          />
          <Animated.Image
            source={images[nextImageIndex]}
            style={[styles.cardBackgroundImage, { opacity: nextFadeAnim, resizeMode: 'cover' }]}
          />
          <View style={styles.cardContent}>
            <View style={styles.cardHeader}>
              <View style={styles.checkinInfo}>
                <ThemedText style={styles.checkinText}>连续签到1天</ThemedText>
                <View style={{ flexDirection: 'row', alignItems: 'flex-end' }}>
                  <ThemedText style={styles.dateText}>12</ThemedText>
                  <ThemedText style={styles.dateSuffix}>th</ThemedText>
                </View>
              </View>
              <LiquidGlassButton
                title=""
                onPress={() => { /* TODO: 分享功能 */ }}
                style={{
                  width: 32,
                  height: 32,
                  borderRadius: 16,
                  shadowColor: '#000',
                  shadowOffset: { width: 0, height: 2 },
                  shadowOpacity: 0.15,
                  shadowRadius: 4,
                  elevation: 3
                }}
                textStyle={{ opacity: 0.8 }}
              >
                <Image source={require('../assets/Forward.png')} style={{ width: 16, height: 16, opacity: 0.8 }} />
              </LiquidGlassButton>
            </View>

            <View style={styles.overlayText}>
              <ThemedText style={styles.overlayText1}>{overlayTexts[currentImageIndex][0]}</ThemedText>
              <ThemedText style={styles.overlayText2}>{overlayTexts[currentImageIndex][1]}</ThemedText>
            </View>

            <Animated.View style={{ transform: [{ scale: buttonScale }] }}>
              <LiquidGlassButton
                title="换一换"
                onPress={handleChangeImage}
                style={{
                  marginTop: 120,
                  width: 80,
                  height: 40,
                  shadowColor: '#000',
                  shadowOffset: { width: 0, height: 2 },
                  shadowOpacity: 0.15,
                  shadowRadius: 4,
                  elevation: 3
                }}
                textStyle={{ fontSize: 14 }}
              />
            </Animated.View>
          </View>
        </View>

        {/* 统计卡片区域 */}
        <View style={styles.statsContainer}>
          <View style={styles.statsCard}>
            <View style={styles.numberContainer}>
              <ThemedText style={styles.statsNumber}>3</ThemedText>
              <ThemedText style={styles.statsUnit}>天</ThemedText>
            </View>
            <ThemedText style={styles.statsLabel}>连续签到</ThemedText>
            <ThemedText style={styles.statsSubLabel}>累计签到1天</ThemedText>
          </View>

          <View style={[styles.statsCard, styles.statsCardBlue]}>
            <View style={styles.numberContainer}>
              <ThemedText style={styles.statsNumber}>0</ThemedText>
              <ThemedText style={styles.statsUnit}>天</ThemedText>
            </View>
            <ThemedText style={styles.statsLabel}>最长连续打卡</ThemedText>
            <ThemedText style={styles.statsSubLabel}>累计打卡1天</ThemedText>
          </View>
        </View>

        {/* 日历区域 */}
        <View style={styles.calendarContainer}>
          <View style={styles.calendarHeader}>
            <TouchableOpacity>
              <ThemedText style={styles.calendarArrow}>‹</ThemedText>
            </TouchableOpacity>
            <ThemedText style={styles.calendarTitle}>{currentMonth}</ThemedText>
            <TouchableOpacity>
              <ThemedText style={styles.calendarArrow}>›</ThemedText>
            </TouchableOpacity>
          </View>

          <View style={styles.weekDays}>
            {['日', '一', '二', '三', '四', '五', '六'].map((day, index) => (
              <ThemedText key={index} style={styles.weekDay}>{day}</ThemedText>
            ))}
          </View>

          <View style={styles.calendarGrid}>
            {Array.from({ length: 31 }, (_, i) => i + 1).map((day) => (
              <View key={day} style={[
                styles.calendarDay,
                day === 10 && styles.checkedDay
              ]}>
                <ThemedText style={[
                  styles.dayText,
                  day === 10 && styles.checkedDayText
                ]}>
                  {day}
                </ThemedText>
              </View>
            ))}
          </View>

          <View style={styles.legend}>
            <View style={styles.legendItem}>
              <View style={styles.legendDot} />
              <ThemedText style={styles.legendText}>当日已签到</ThemedText>
            </View>
          </View>
        </View>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFF8F3',
  },
  safeArea: {
    height: 50,
    backgroundColor: '#FFF8F3',
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    zIndex: 15,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingTop: 10,
    paddingBottom: 12,
    backgroundColor: '#FFF8F3',
    marginBottom: 4,
  },
  backIcon: {
    fontSize: 24,
    color: '#333',
    fontWeight: 'bold',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.light.title,
  },
  headerRight: {
    width: 24,
  },
  content: {
    flex: 1,
    backgroundColor: '#FFF8F3',
  },
  mainCard: {
    borderRadius: 16,
    marginTop: 0,
    marginBottom: 16,
    marginHorizontal: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
    overflow: 'hidden',
    position: 'relative',
    backgroundColor: '#FFF8F3',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 0,
    minHeight: 380,
    height: 380,
    paddingTop: 0,
  },
  cardBackgroundImage: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    width: '100%',
    height: '100%', // 让图片完全撑满卡片
    resizeMode: 'cover', // 让图片撑满且无留白
  },
  cardContent: {
    position: 'relative',
    zIndex: 1,
    width: '100%',
    alignItems: 'center', // 恢复居中
    paddingTop: 0,
    paddingBottom: 20,
    paddingHorizontal: 20, // 恢复内边距
    backgroundColor: 'transparent',
  },
  cardHeader: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    justifyContent: 'space-between',
    width: '100%',
    marginBottom: 0,
    paddingTop: 0,
    paddingLeft: 0,
    paddingRight: 0,
    marginTop: 0, // 恢复为0
  },
  checkinInfo: {
    alignItems: 'flex-start', // 靠左
    justifyContent: 'flex-start',
  },
  checkinText: {
    fontSize: 14,
    color: '#fff',
    marginBottom: 4,
    textAlign: 'left', // 确保文本左对齐
  },
  dateText: {
    fontSize: 32,
    fontWeight: 'bold',
    color: '#fff',
  },
  dateSuffix: {
    fontSize: 13,
    color: '#fff',
    marginLeft: 2,
    marginBottom: 3,
    fontWeight: 'normal',
  },

  motivationalText: {
    fontSize: 16,
    color: '#fff',
    textAlign: 'center',
    marginBottom: 16,
  },

  overlayText: {
    marginTop: 40,
    marginBottom: 0,
    padding: 12,
    borderRadius: 8,
  },
  overlayText1: {
    fontSize: 14,
    color: '#fff',
    fontWeight: 'bold',
    marginBottom: 2,
  },
  overlayText2: {
    fontSize: 12,
    color: '#fff',
    textAlign: 'center',
  },

  statsContainer: {
    flexDirection: 'row',
    marginBottom: 20,
    marginHorizontal: 16,
  },
  statsCard: {
    flex: 1,
    backgroundColor: '#FFD29B',
    borderRadius: 12,
    padding: 16,
    marginRight: 8,
    alignItems: 'flex-start', // 改为左对齐
    boxShadow: '0px 6px 16px rgba(0, 0, 0, 0.08)',
    elevation: 6, // Android阴影
  },
  statsCardBlue: {
    backgroundColor: '#E5F5FF',
    marginRight: 0,
    marginLeft: 8,
    boxShadow: '0px 6px 16px rgba(0, 0, 0, 0.08)',
    elevation: 6, // Android阴影
  },
  numberContainer: {
    flexDirection: 'row',
    alignItems: 'baseline',
    marginBottom: 0,
  },
  statsNumber: {
    fontSize: 48, // 增大数字字体
    fontWeight: 'bold',
    color: '#222222',
    textAlign: 'left',
  },
  statsUnit: {
    fontSize: 16, // 较小的单位字体
    fontWeight: 'bold',
    color: '#222222',
    marginLeft: 2,
  },
  statsLabel: {
    fontSize: 12,
    color: '#333333',
    marginBottom: 2,
    textAlign: 'left', // 确保文本左对齐
  },
  statsSubLabel: {
    fontSize: 10,
    color: 'rgba(145, 138, 138, 0.8)',
    textAlign: 'left', // 确保文本左对齐
  },
  calendarContainer: {
    backgroundColor: '#fff',
    borderRadius: 16,
    padding: 16,
    marginBottom: 20,
    marginHorizontal: 16,
    ...shadowPresets.card,
  },
  calendarHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  calendarArrow: {
    fontSize: 20,
    color: Colors.light.iconColor,
    fontWeight: 'bold',
  },
  calendarTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: Colors.light.title,
  },
  weekDays: {
    flexDirection: 'row',
    marginBottom: 12,
  },
  weekDay: {
    flex: 1,
    textAlign: 'center',
    fontSize: 14,
    color: '#ad7d42ff',
    fontWeight: 'bold',
  },
  calendarGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginBottom: 16,
  },
  calendarDay: {
    width: '14.28%',
    aspectRatio: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  checkedDay: {
    backgroundColor: '#FFD29B',
    borderRadius: 50,
    padding: 0,
  },
  dayText: {
    fontSize: 14,
    color: Colors.light.title,
  },
  checkedDayText: {
    color: '#fff',
    fontWeight: 'bold',
    fontSize: 13,
  },
  legend: {
    alignItems: 'center',
  },
  legendItem: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  legendDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: '#FFD29B',
    marginRight: 6,
  },
  legendText: {
    fontSize: 12,
    color: Colors.light.iconColor,
  },
});

export default LearningCalendar; 