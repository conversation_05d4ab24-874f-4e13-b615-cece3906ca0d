import { Keyboard, StyleSheet, Text, Pressable, View, TouchableOpacity, Image, ScrollView } from 'react-native'
import { Link, router } from 'expo-router'
import { useState } from 'react'
import { useUser } from '../../hooks/useUser'

import ThemedView from '../../components/ThemedView'
import ThemedText from '../../components/ThemedText'
import Spacer from '../../components/Spacer'
import ThemedTextInput from "../../components/ThemedTextInput"
import GradientGlassButton from '../../components/GradientGlassButton'
import CustomCheckbox from '../../components/CustomCheckbox'
import { Colors } from '../../constants/Colors'

const Register = () => {
  const [email, setEmail] = useState("")
  const [password, setPassword] = useState("")
  const [confirmPassword, setConfirmPassword] = useState("")
  const [error, setError] = useState(null)
  const [showPassword, setShowPassword] = useState(false)
  const [showConfirmPassword, setShowConfirmPassword] = useState(false)
  const [agreedToTerms, setAgreedToTerms] = useState(false) // 新增：用户协议同意状态

  const { user, register } = useUser()

  const handleSubmit = async () => {
    setError(null)

    // 检查是否同意用户协议
    if (!agreedToTerms) {
      setError("请先阅读并同意《用户协议》和《隐私授权》")
      return
    }

    // 验证密码确认
    if (password !== confirmPassword) {
      setError("密码和确认密码不一致")
      return
    }

    if (!email || !password || !confirmPassword) {
      setError("请填写所有字段")
      return
    }

    try {
      const result = await register(email, password, confirmPassword)
      console.log('注册结果:', result)

      // 注册成功后跳转到登录页面，让用户手动登录
      console.log('注册成功，跳转到登录页面')
      router.replace('/(auth)/login')
    } catch (error) {
      setError(error.message)
    }
  }

  return (
    <Pressable onPress={Keyboard.dismiss} style={{ flex: 1 }}>
      <ThemedView style={styles.container} safe={false}>
        <ScrollView
          style={styles.scrollView}
          contentContainerStyle={styles.scrollContent}
          showsVerticalScrollIndicator={false}
          keyboardShouldPersistTaps="handled"
        >
          <Spacer height={15} />

          {/* 返回按钮 */}
          <TouchableOpacity style={styles.backButton} onPress={() => router.push('/(auth)/login')}>
            <Image source={require('../../assets/Arrows_left.png')} style={styles.backButtonIcon} />
          </TouchableOpacity>

          <Spacer height={30} />

          {/* 标题 */}
          <View style={styles.titleContainer}>
            <ThemedText title={true} style={styles.title}>
              注册
            </ThemedText>
            <ThemedText style={styles.subtitle}>
              我们等你好久了！
            </ThemedText>
          </View>

          <Spacer height={30} />

          {/* 白色表单容器 */}
          <View style={styles.whiteContainer}>
          {/* 表单 */}
          <View style={styles.formContainer}>
          <ThemedText style={styles.fieldLabel}>账号</ThemedText>
          <View style={styles.inputContainer}>
            <Image source={require('../../assets/Auth_image/People_number.png')} style={styles.inputIcon} />
            <ThemedTextInput
              style={[styles.input, styles.inputWithIcon]}
              placeholder="请输入账号"
              value={email}
              onChangeText={setEmail}
              keyboardType="email-address"
            />
          </View>

          <Spacer height={20} />
          <ThemedText style={styles.fieldLabel}>密码</ThemedText>
          <View style={styles.passwordContainer}>
            <Image source={require('../../assets/Auth_image/Lock.png')} style={styles.inputIcon} />
            <ThemedTextInput
              style={[styles.input, styles.passwordInput]}
              placeholder="请输入密码"
              value={password}
              onChangeText={setPassword}
              secureTextEntry={!showPassword}
            />
            <TouchableOpacity
              style={styles.eyeButton}
              onPress={() => setShowPassword(!showPassword)}
            >
              <Image
                source={showPassword ? require('../../assets/Auth_image/Eyes_open.png') : require('../../assets/Auth_image/Eyes_close.png')}
                style={styles.eyeIcon}
              />
            </TouchableOpacity>
          </View>

          <Spacer height={20} />
          <ThemedText style={styles.fieldLabel}>确认密码</ThemedText>
          <View style={styles.passwordContainer}>
            <Image source={require('../../assets/Auth_image/Key.png')} style={styles.inputIcon} />
            <ThemedTextInput
              style={[styles.input, styles.passwordInput]}
              placeholder="请再次输入密码"
              value={confirmPassword}
              onChangeText={setConfirmPassword}
              secureTextEntry={!showConfirmPassword}
            />
            <TouchableOpacity
              style={styles.eyeButton}
              onPress={() => setShowConfirmPassword(!showConfirmPassword)}
            >
              <Image
                source={showConfirmPassword ? require('../../assets/Auth_image/Eyes_open.png') : require('../../assets/Auth_image/Eyes_close.png')}
                style={styles.eyeIcon}
              />
            </TouchableOpacity>
          </View>
        </View>


          {/* 注册按钮 */}
          <GradientGlassButton
            title="注册"
            onPress={handleSubmit}
            style={[styles.registerButton, !agreedToTerms && styles.disabledButton]}
            disabled={!agreedToTerms}
          />

          {/* 用户协议 */}
          <View style={styles.agreementContainer}>
            <CustomCheckbox
              checked={agreedToTerms}
              onPress={() => setAgreedToTerms(!agreedToTerms)}
              checkedColor="#FFC885"
              size={15}
              style={styles.checkbox}
            />
            <Text style={styles.agreementText}>
              我已阅读并同意
              <Text style={styles.agreementLink}>《用户协议》</Text>
              和
              <Text style={styles.agreementLink}>《隐私授权》</Text>
            </Text>
          </View>
          <Spacer />
          {error && <Text style={styles.error}>{error}</Text>}

          {/* 登录链接 */}
          <View style={styles.loginLinkContainer}>
            <Text style={styles.loginPrompt}>已有账号？</Text>
            <TouchableOpacity onPress={() => router.push('/(auth)/login')}>
              <Text style={styles.loginLink}>立即登录</Text>
            </TouchableOpacity>
          </View>
          {/* 第三方登录 */}
          <View style={styles.thirdPartyContainer}>
            <View style={styles.thirdPartyTitleContainer}>
              <View style={styles.thirdPartyLine} />
              <Text style={styles.thirdPartyTitle}>第三方账号登录</Text>
              <View style={styles.thirdPartyLine} />
            </View>
            <View style={styles.thirdPartyButtons}>
              <TouchableOpacity style={styles.thirdPartyButton}>
                <Image source={require('../../assets/Auth_image/QQ.png')} style={styles.thirdPartyIcon} />
              </TouchableOpacity>
              <TouchableOpacity style={styles.thirdPartyButton}>
                <Image source={require('../../assets/Auth_image/Wechat.png')} style={styles.forthPartyIcon} />
              </TouchableOpacity>
            </View>
          </View>

        </View>
        {/* 白色容器结束 */}

        <Spacer height={40} />

        </ScrollView>

      </ThemedView>
    </Pressable>
  )
}

export default Register

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
    minHeight: '100%', // 确保内容至少铺满整个屏幕
    paddingHorizontal: 10,
    paddingTop: 20,
  },
  backButton: {
    alignSelf: 'flex-start',
    marginLeft: 10,
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
  backButtonText: {
    fontSize: 24,
    color: Colors.boldText,
  },
  backButtonIcon: {
    width: 24,
    height: 24,
  },
  titleContainer: {
    alignItems: 'flex-start',
    paddingHorizontal: 20,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    marginBottom: 10,
    color: Colors.boldText,
  },
  subtitle: {
    fontSize: 16,
    color: Colors.boldText,
    marginBottom: 20,
  },
  whiteContainer: {
    backgroundColor: '#FFFFFF',
    borderRadius: 24,
    marginHorizontal: 16,
    paddingHorizontal: 20,
    paddingVertical: 30,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.12,
    shadowRadius: 12,
    elevation: 6,
    marginBottom: 20,
  },
  formContainer: {
    paddingHorizontal: 0, // 移除内边距，因为白色容器已有
  },
  fieldLabel: {
    fontSize: 16,
    color: Colors.boldText,
    marginBottom: 12,
    fontWeight: '500',
  },
  inputContainer: {
    position: 'relative',
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  inputIcon: {
    position: 'absolute',
    left: 16, // 将图标稍微向左移动
    width: 20,
    height: 20,
    zIndex: 1,
  },
  input: {
    backgroundColor: '#F5F5F5',
    borderRadius: 36,
    paddingHorizontal: 20,
    paddingVertical: 15,
    fontSize: 16,
    color: Colors.normalText,
    padding: 0, // 重置ThemedTextInput的默认padding
  },
  inputWithIcon: {
    paddingLeft: 45, // 为图标留出空间
    paddingRight: 20,
    paddingVertical: 15,
    flex: 1,
  },
  passwordContainer: {
    position: 'relative',
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  passwordInput: {
    paddingLeft: 45,  // 为锁图标留出空间
    paddingRight: 56, // 为眼睛图标留出空间
    paddingVertical: 15,
    flex: 1,
  },
  eyeButton: {
    position: 'absolute',
    right: 20,
    top: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 4,
  },
  eyeIcon: {
    width: 20,
    height: 20,
  },
  registerButton: {
    marginBottom: 20,
  },
  agreementContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 0,
  },
  checkbox: {
    marginRight: 12,
  },
  disabledButton: {
    opacity: 0.5,
  },
  agreementText: {
    fontSize: 12,
    color: Colors.normalText,
    flex: 1,
  },
  agreementLink: {
    color: '#7A3C10',
    fontSize: 12,
  },
  thirdPartyContainer: {
    alignItems: 'center',
    marginTop: 20,
    marginBottom: 0, // 移除底部边距，因为在白色容器内
  },
  thirdPartyTitleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 20,
    width: '100%',
  },
  thirdPartyLine: {
    flex: 1,
    height: 1,
    backgroundColor: '#E0E0E0',
    marginHorizontal: 15,
  },
  thirdPartyTitle: {
    fontSize: 14,
    color: Colors.normalText,
    paddingHorizontal: 10,
  },
  thirdPartyButtons: {
    flexDirection: 'row',
    justifyContent: 'center',
  },
  thirdPartyButton: {
    width: 40,
    height: 40,
    borderRadius: 25,
    backgroundColor: Colors.button, // 使用按钮颜色常量
    justifyContent: 'center',
    alignItems: 'center',
    marginHorizontal: 33,
  },
  thirdPartyIcon: {
    width: 24,
    height: 24,
    resizeMode: 'contain',
  },
  forthPartyIcon: {
    width: 28,
    height: 28,
    resizeMode: 'contain',
  },
  loginLinkContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 20,
  },
  loginPrompt: {
    fontSize: 14,
    color: Colors.normalText,
    marginRight: 5,
  },
  loginLink: {
    fontSize: 14,
    color: '#7A3C10',
    fontWeight: '600',
  },
  error: {
    color: Colors.warning,
    padding: 10,
    backgroundColor: '#f5c1c8',
    borderColor: Colors.warning,
    borderWidth: 1,
    borderRadius: 6,
    marginHorizontal: 10,
    textAlign: 'center',
  }
})
