import React, { useState } from 'react';
import { StyleSheet, View, TouchableOpacity, Image, ScrollView, TextInput } from 'react-native';
import { useRouter } from 'expo-router';
import ThemedText from '../../components/ThemedText';
import ThemedView from '../../components/ThemedView';


const InductiveRecommendation = () => {
  const router = useRouter();
  const [searchText, setSearchText] = useState('');

  return (
    <ThemedView style={styles.container}>
      {/* 安全视图 */}
      <View style={styles.safeArea} />

      <ScrollView style={styles.scrollView} contentContainerStyle={{ paddingTop: 60 }} showsVerticalScrollIndicator={false}>
        {/* 顶部标题栏 */}
        <View style={styles.header}>
          <TouchableOpacity onPress={() => router.back()} style={styles.backButton}>
            <Image source={require('../../assets/FrameTwo.png')} style={styles.backIcon} />
          </TouchableOpacity>
          <ThemedText style={styles.headerTitle}>练习题推荐</ThemedText>
          <View style={styles.headerRight} />
        </View>

        <View style={styles.content}>


          {/* 今日推荐练习 */}
          <View style={styles.section}>
            <ThemedText style={styles.sectionTitle}>今日推荐练习</ThemedText>
            <View style={styles.todayCard}>
              <View style={styles.todayCardLeft}>
                <View style={styles.placeholderImage} />
              </View>
              <View style={styles.todayCardRight}>
                <ThemedText style={styles.todayCardTitle}>2021《会计》历年真题</ThemedText>
                <ThemedText style={styles.todayCardSubtitle}>2000+人已刷题</ThemedText>
                <View style={styles.todayCardButtons}>
                  <TouchableOpacity style={styles.basicButton}>
                    <ThemedText style={styles.basicButtonText}>基础练习</ThemedText>
                  </TouchableOpacity>
                  <TouchableOpacity style={styles.startButton}>
                    <ThemedText style={styles.startButtonText}>开始</ThemedText>
                  </TouchableOpacity>
                </View>
                <View style={styles.exchangeContainer}>
                  <ThemedText style={styles.exchangeText}>换一换</ThemedText>
                </View>
              </View>
            </View>
          </View>

          {/* 知识点专项突破 */}
          <View style={styles.section}>
            <ThemedText style={styles.sectionTitle}>知识点专项突破</ThemedText>
            <ThemedText style={styles.sectionSubtitle}>薄弱点强化</ThemedText>
            <ThemedText style={styles.sectionDesc}>自动推荐高频错误知识点</ThemedText>

            <View style={styles.knowledgeGrid}>
              <View style={styles.knowledgeMainRow}>
                <View style={styles.knowledgeLeftColumn}>
                  <TouchableOpacity style={[styles.knowledgeCard, styles.knowledgeCardMedium]}>
                    <ThemedText style={styles.knowledgeCardTitle}>三角函数</ThemedText>
                    <View style={styles.knowledgeCardIcon}>
                      {/* 几何图形装饰 */}
                      <View style={styles.geometryShape1} />
                      <View style={styles.geometryShape2} />
                      <View style={styles.geometryShape3} />
                    </View>
                  </TouchableOpacity>
                  <TouchableOpacity style={[styles.knowledgeCard, styles.knowledgeCardMedium]}>
                    <ThemedText style={styles.knowledgeCardTitle}>词汇辨析</ThemedText>
                  </TouchableOpacity>
                </View>
                <TouchableOpacity style={[styles.knowledgeCard, styles.knowledgeCardTall]}>
                  <ThemedText style={styles.knowledgeCardTitle}>语法结构</ThemedText>
                  <ThemedText style={styles.knowledgeCardSubtitle}>ABC</ThemedText>
                </TouchableOpacity>
              </View>
            </View>
          </View>

          {/* 微课+练习组合 */}
          <View style={styles.section}>
            <ThemedText style={styles.sectionTitle}>微课+练习组合</ThemedText>
            <ThemedText style={styles.sectionDesc}>先看7分钟微课技巧讲解，再做练习</ThemedText>

            <View style={styles.microCourseGrid}>
              <TouchableOpacity style={styles.microCourseCard}>
                <View style={styles.microCoursePlaceholder} />
              </TouchableOpacity>
              <TouchableOpacity style={styles.microCourseCard}>
                <View style={styles.microCoursePlaceholder} />
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </ScrollView>
    </ThemedView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  scrollView: {
    flex: 1,
  },
  safeArea: {
    height: 50,
    backgroundColor: '#f5f5f5',
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    zIndex: 15,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingBottom: 16,
    paddingTop: 0,
    backgroundColor: 'transparent',
  },
  backButton: {
    padding: 8,
  },
  backIcon: {
    width: 24,
    height: 24,
    resizeMode: 'contain',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  headerRight: {
    width: 32,
  },
  content: {
    paddingHorizontal: 16,
  },
  searchContainer: {
    marginVertical: 16,
  },
  searchBox: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#e8e8e8',
    borderRadius: 20,
    paddingHorizontal: 16,
    height: 40,
  },
  searchIcon: {
    width: 16,
    height: 16,
    marginRight: 8,
  },
  searchInput: {
    flex: 1,
    fontSize: 14,
    color: '#333',
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 16,
  },
  sectionSubtitle: {
    fontSize: 14,
    color: '#666',
    marginBottom: 2,
  },
  sectionDesc: {
    fontSize: 12,
    color: '#999',
    marginBottom: 12,
  },
  todayCard: {
    flexDirection: 'row',
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    boxShadow: '0px 2px 4px rgba(0, 0, 0, 0.1)',
    elevation: 3,
  },
  todayCardLeft: {
    marginRight: 16,
  },
  placeholderImage: {
    width: 80,
    height: 80,
    backgroundColor: '#e0e0e0',
    borderRadius: 8,
  },
  todayCardRight: {
    flex: 1,
  },
  todayCardTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 4,
  },
  todayCardSubtitle: {
    fontSize: 12,
    color: '#999',
    marginBottom: 12,
  },
  todayCardButtons: {
    flexDirection: 'row',
    marginBottom: 8,
  },
  basicButton: {
    backgroundColor: '#e0e0e0',
    paddingHorizontal: 16,
    paddingVertical: 6,
    borderRadius: 16,
    marginRight: 8,
  },
  basicButtonText: {
    fontSize: 12,
    color: '#666',
  },
  startButton: {
    backgroundColor: '#f0f0f0',
    paddingHorizontal: 16,
    paddingVertical: 6,
    borderRadius: 16,
  },
  startButtonText: {
    fontSize: 12,
    color: '#333',
  },
  exchangeContainer: {
    alignItems: 'flex-end',
  },
  exchangeText: {
    fontSize: 12,
    color: '#999',
  },
  knowledgeGrid: {
    gap: 8,
  },
  knowledgeRow: {
    flexDirection: 'row',
    gap: 8,
    marginBottom: 8,
  },
  knowledgeMainRow: {
    flexDirection: 'row',
    gap: 8,
  },
  knowledgeLeftColumn: {
    flex: 1,
    gap: 8,
  },
  knowledgeCard: {
    backgroundColor: '#e8e8e8',
    borderRadius: 12,
    padding: 16,
    justifyContent: 'center',
  },
  knowledgeCardLarge: {
    flex: 1,
    height: 100,
  },
  knowledgeCardSmall: {
    width: 100,
    height: 100,
  },
  knowledgeCardMedium: {
    height: 100,
    flex: 1,
  },
  knowledgeCardTall: {
    width: 100,
    height: 208, // 100 + 8(gap) + 100 = 208，填补整个右侧空间
  },
  knowledgeCardTitle: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#333',
  },
  knowledgeCardSubtitle: {
    fontSize: 24,
    color: '#ccc',
    marginTop: 8,
  },
  knowledgeCardIcon: {
    position: 'absolute',
    right: 16,
    top: 16,
  },
  geometryShape1: {
    width: 12,
    height: 12,
    backgroundColor: '#ccc',
    borderRadius: 6,
    position: 'absolute',
    top: 0,
    right: 0,
  },
  geometryShape2: {
    width: 8,
    height: 8,
    backgroundColor: '#ddd',
    borderRadius: 4,
    position: 'absolute',
    top: 8,
    right: 16,
  },
  geometryShape3: {
    width: 6,
    height: 6,
    backgroundColor: '#eee',
    borderRadius: 3,
    position: 'absolute',
    top: 16,
    right: 8,
  },
  microCourseGrid: {
    flexDirection: 'row',
    gap: 12,
  },
  microCourseCard: {
    flex: 1,
    height: 200,
    backgroundColor: '#e8e8e8',
    borderRadius: 12,
  },
  microCoursePlaceholder: {
    flex: 1,
    backgroundColor: '#e0e0e0',
    borderRadius: 12,
  },
});

export default InductiveRecommendation;