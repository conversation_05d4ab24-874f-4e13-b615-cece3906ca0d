import { createContext, useContext, useState } from 'react';

export const RecommendationContext = createContext();

export const RecommendationProvider = ({ children }) => {
  const [recommendations, setRecommendations] = useState(null);

  return (
    <RecommendationContext.Provider value={{ recommendations, setRecommendations }}>
      {children}
    </RecommendationContext.Provider>
  );
};

export const useRecommendation = () => useContext(RecommendationContext);
