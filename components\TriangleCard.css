.industry-info {
  padding: 16px;
}

.industry-info__header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px;
}

.triangle-row {
  display: flex;
  gap: 16px;
  flex-wrap: wrap;
  justify-content: center;
}

.svg-triangle-container {
  width: 120px;
  height: 120px;
  flex: 0 1 120px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.svg-triangle {
  width: 100%;
  height: 100%;
}

@media (max-width: 600px) {
  .svg-triangle-container {
    width: 80px;
    height: 80px;
    flex: 0 1 80px;
  }

  .triangle-row {
    gap: 8px;
  }
}