import React, { useState, useRef, useEffect } from 'react';
import { StyleSheet, View, TouchableOpacity, ScrollView, Image, Animated } from 'react-native';
import { router } from 'expo-router';
import { LinearGradient } from 'expo-linear-gradient';
import ThemedText from '../components/ThemedText';
import ThemedView from '../components/ThemedView';
import GradientGlassButton from '../components/GradientGlassButton';
import { Ionicons } from '@expo/vector-icons';

export default function PlanGoalEdit() {
  const [shortTermGoal, setShortTermGoal] = useState('考试');
  const [longTermGoal, setLongTermGoal] = useState('英语四六级');
  
  // 时间段选项
  const timeOptions = ['不重复', '每日', '每周', '每年', '法定工作日'];
  const [shortTermSelectedTimeOption, setShortTermSelectedTimeOption] = useState('每日');
  const [longTermSelectedTimeOption, setLongTermSelectedTimeOption] = useState('每日');
  
  // 短期目标图标状态管理
  const [shortTermIcons, setShortTermIcons] = useState({
    time: false,
    repeat: false,
    remind: false
  });

  // 长期目标图标状态管理
  const [longTermIcons, setLongTermIcons] = useState({
    time: false,
    repeat: false,
    remind: false
  });

  const handleShortTermIconPress = (iconType) => {
    // 控制显示的内容
    if (shortTermActiveContent === iconType) {
      // 如果已经选中，则隐藏内容并重置所有图标
      animateContent(shortTermContentHeight, shortTermContentOpacity, false);
      setTimeout(() => {
        setShortTermActiveContent(null);
      }, 200);
      setShortTermIcons({
        time: false,
        repeat: false,
        remind: false
      });
    } else {
      // 如果有其他内容正在显示，先隐藏再显示新内容
      if (shortTermActiveContent) {
        animateContent(shortTermContentHeight, shortTermContentOpacity, false);
        setTimeout(() => {
          setShortTermActiveContent(iconType);
          setShortTermIcons({
            time: iconType === 'time',
            repeat: iconType === 'repeat',
            remind: iconType === 'remind'
          });
          setTimeout(() => {
            animateContent(shortTermContentHeight, shortTermContentOpacity, true);
          }, 50);
        }, 200);
      } else {
        // 直接显示新内容
        setShortTermActiveContent(iconType);
        setShortTermIcons({
          time: iconType === 'time',
          repeat: iconType === 'repeat',
          remind: iconType === 'remind'
        });
        setTimeout(() => {
          animateContent(shortTermContentHeight, shortTermContentOpacity, true);
        }, 50);
      }
    }
  };

  const handleLongTermIconPress = (iconType) => {
    // 控制显示的内容
    if (longTermActiveContent === iconType) {
      // 如果已经选中，则隐藏内容并重置所有图标
      animateContent(longTermContentHeight, longTermContentOpacity, false);
      setTimeout(() => {
        setLongTermActiveContent(null);
      }, 200);
      setLongTermIcons({
        time: false,
        repeat: false,
        remind: false
      });
    } else {
      // 如果有其他内容正在显示，先隐藏再显示新内容
      if (longTermActiveContent) {
        animateContent(longTermContentHeight, longTermContentOpacity, false);
        setTimeout(() => {
          setLongTermActiveContent(iconType);
          setLongTermIcons({
            time: iconType === 'time',
            repeat: iconType === 'repeat',
            remind: iconType === 'remind'
          });
          setTimeout(() => {
            animateContent(longTermContentHeight, longTermContentOpacity, true);
          }, 50);
        }, 200);
      } else {
        // 直接显示新内容
        setLongTermActiveContent(iconType);
        setLongTermIcons({
          time: iconType === 'time',
          repeat: iconType === 'repeat',
          remind: iconType === 'remind'
        });
        setTimeout(() => {
          animateContent(longTermContentHeight, longTermContentOpacity, true);
        }, 50);
      }
    }
  };

  // 控制显示内容的状态
  const [shortTermActiveContent, setShortTermActiveContent] = useState(null);
  const [longTermActiveContent, setLongTermActiveContent] = useState(null);

  // 动画值
  const shortTermContentHeight = useRef(new Animated.Value(0)).current;
  const shortTermContentOpacity = useRef(new Animated.Value(0)).current;
  const longTermContentHeight = useRef(new Animated.Value(0)).current;
  const longTermContentOpacity = useRef(new Animated.Value(0)).current;

  const handleBack = () => {
    // 使用back()获得正确的返回动画方向
    router.back();
  };

  const handleAddTask = () => {
    console.log('保存成功');
    // 保存成功后回到计划页面的目标设定分页
    router.push('/(dashboard)/plans?tab=2');
  };

  // 动画函数
  const animateContent = (heightValue, opacityValue, show) => {
    Animated.parallel([
      Animated.timing(heightValue, {
        toValue: show ? 1 : 0,
        duration: 250,
        useNativeDriver: false,
      }),
      Animated.timing(opacityValue, {
        toValue: show ? 1 : 0,
        duration: 200,
        useNativeDriver: false,
      }),
    ]).start();
  };

  return (
    <ThemedView style={styles.container}>
      <ScrollView contentContainerStyle={styles.content} showsVerticalScrollIndicator={false}>
        {/* 顶部导航 */}
        <View style={styles.header}>
          <TouchableOpacity onPress={handleBack} style={styles.backButton}>
            <Ionicons name="chevron-back" size={28} color="#222" />
          </TouchableOpacity>
          <ThemedText style={styles.headerTitle}>目标修改</ThemedText>
        </View>

        {/* 内容包裹盒子 */}
        <View style={styles.contentWrapper}>
          {/* 短期目标卡片 */}
          <View style={styles.goalCard}>
            <View style={styles.goalTitleBox}>
              <ThemedText style={styles.goalTitle}>短期目标</ThemedText>
            </View>
            <View style={styles.goalContentBox}>
            <View style={styles.goalItemRow}>
              <Image source={require('../assets/Plan_image/Target.png')} style={styles.goalIcon} />
              <ThemedText style={styles.goalText}>{shortTermGoal}</ThemedText>
            </View>

            {/* 分隔线 */}
            <View style={styles.dividerLine} />
            
            {/* 功能图标行 */}
            <View style={styles.iconRow}>
              <TouchableOpacity
                style={styles.iconButton}
                onPress={() => handleShortTermIconPress('time')}
                activeOpacity={0.7}
              >
                <Image
                  source={shortTermIcons.time
                    ? require('../assets/Plan_image/Time_1.png')
                    : require('../assets/Plan_image/Time_0.png')
                  }
                  style={styles.functionIcon}
                />
                <ThemedText style={[styles.iconLabel, shortTermIcons.time ? styles.iconLabelActive : styles.iconLabelInactive]}>时间段</ThemedText>
              </TouchableOpacity>

              <TouchableOpacity
                style={styles.iconButton}
                onPress={() => handleShortTermIconPress('repeat')}
                activeOpacity={0.7}
              >
                <Image
                  source={shortTermIcons.repeat
                    ? require('../assets/Plan_image/Repetition_1.png')
                    : require('../assets/Plan_image/Repetition_0.png')
                  }
                  style={styles.functionIcon}
                />
                <ThemedText style={[styles.iconLabel, shortTermIcons.repeat ? styles.iconLabelActive : styles.iconLabelInactive]}>重复</ThemedText>
              </TouchableOpacity>

              <TouchableOpacity
                style={styles.iconButton}
                onPress={() => handleShortTermIconPress('remind')}
                activeOpacity={0.7}
              >
                <Image
                  source={shortTermIcons.remind
                    ? require('../assets/Plan_image/Remind_1.png')
                    : require('../assets/Plan_image/Remind_0.png')
                  }
                  style={styles.functionIcon}
                />
                <ThemedText style={[styles.iconLabel, shortTermIcons.remind ? styles.iconLabelActive : styles.iconLabelInactive]}>提醒</ThemedText>
              </TouchableOpacity>
            </View>

            {/* 根据选中的图标显示对应内容 */}
            {shortTermActiveContent && (
              <Animated.View style={[
                styles.animatedContainer,
                {
                  maxHeight: shortTermContentHeight.interpolate({
                    inputRange: [0, 1],
                    outputRange: [0, 150],
                  }),
                  opacity: shortTermContentOpacity,
                }
              ]}>
                {shortTermActiveContent === 'repeat' && (
                  <View style={styles.timeOptionsContainer}>
                    {timeOptions.map((option, index) => (
                      <TouchableOpacity
                        key={index}
                        style={[
                          styles.timeOption,
                          shortTermSelectedTimeOption === option && styles.timeOptionSelected
                        ]}
                        onPress={() => setShortTermSelectedTimeOption(option)}
                        activeOpacity={0.8}
                      >
                        <ThemedText style={[
                          styles.timeOptionText,
                          shortTermSelectedTimeOption === option && styles.timeOptionTextSelected
                        ]}>
                          {option}
                        </ThemedText>
                      </TouchableOpacity>
                    ))}
                  </View>
                )}

                {shortTermActiveContent === 'time' && (
                  <View style={styles.timeInfoContainer}>
                    <View style={styles.timeInfoBox}>
                      <ThemedText style={styles.timeInfoLabel}>开始</ThemedText>
                      <ThemedText style={styles.timeInfoValue}>7月27日 周日</ThemedText>
                      <ThemedText style={styles.timeInfoSubValue}>今天</ThemedText>
                    </View>
                    <View style={styles.timeInfoBox}>
                      <ThemedText style={styles.timeInfoLabel}>结束</ThemedText>
                      <ThemedText style={styles.timeInfoValue}>可设计划所需天数</ThemedText>
                      <ThemedText style={styles.timeInfoSubValue}>今天</ThemedText>
                    </View>
                  </View>
                )}

                {shortTermActiveContent === 'remind' && (
                  <View style={styles.remindContainer}>
                    <ThemedText style={styles.remindText}>提醒设置</ThemedText>
                    <ThemedText style={styles.remindSubText}>设置提醒时间和方式</ThemedText>
                  </View>
                )}
              </Animated.View>
            )}
          </View>
        </View>

        {/* 长期目标卡片 */}
        <View style={styles.goalCard}>
          <View style={styles.goalTitleBox}>
            <ThemedText style={styles.goalTitle}>长期目标</ThemedText>
          </View>
          <View style={styles.goalContentBox}>
            <View style={styles.goalItemRow}>
              <Image source={require('../assets/Plan_image/Target.png')} style={styles.goalIcon} />
              <ThemedText style={styles.goalText}>{longTermGoal}</ThemedText>
            </View>

            {/* 分隔线 */}
            <View style={styles.dividerLine} />
            
            {/* 功能图标行 */}
            <View style={styles.iconRow}>
              <TouchableOpacity
                style={styles.iconButton}
                onPress={() => handleLongTermIconPress('time')}
                activeOpacity={0.7}
              >
                <Image
                  source={longTermIcons.time
                    ? require('../assets/Plan_image/Time_1.png')
                    : require('../assets/Plan_image/Time_0.png')
                  }
                  style={styles.functionIcon}
                />
                <ThemedText style={[styles.iconLabel, longTermIcons.time ? styles.iconLabelActive : styles.iconLabelInactive]}>时间段</ThemedText>
              </TouchableOpacity>

              <TouchableOpacity
                style={styles.iconButton}
                onPress={() => handleLongTermIconPress('repeat')}
                activeOpacity={0.7}
              >
                <Image
                  source={longTermIcons.repeat
                    ? require('../assets/Plan_image/Repetition_1.png')
                    : require('../assets/Plan_image/Repetition_0.png')
                  }
                  style={styles.functionIcon}
                />
                <ThemedText style={[styles.iconLabel, longTermIcons.repeat ? styles.iconLabelActive : styles.iconLabelInactive]}>重复</ThemedText>
              </TouchableOpacity>

              <TouchableOpacity
                style={styles.iconButton}
                onPress={() => handleLongTermIconPress('remind')}
                activeOpacity={0.7}
              >
                <Image
                  source={longTermIcons.remind
                    ? require('../assets/Plan_image/Remind_1.png')
                    : require('../assets/Plan_image/Remind_0.png')
                  }
                  style={styles.functionIcon}
                />
                <ThemedText style={[styles.iconLabel, longTermIcons.remind ? styles.iconLabelActive : styles.iconLabelInactive]}>提醒</ThemedText>
              </TouchableOpacity>
            </View>

            {/* 根据选中的图标显示对应内容 */}
            {longTermActiveContent && (
              <Animated.View style={[
                styles.animatedContainer,
                {
                  maxHeight: longTermContentHeight.interpolate({
                    inputRange: [0, 1],
                    outputRange: [0, 150],
                  }),
                  opacity: longTermContentOpacity,
                }
              ]}>
                {longTermActiveContent === 'repeat' && (
                  <View style={styles.timeOptionsContainer}>
                    {timeOptions.map((option, index) => (
                      <TouchableOpacity
                        key={index}
                        style={[
                          styles.timeOption,
                          longTermSelectedTimeOption === option && styles.timeOptionSelected
                        ]}
                        onPress={() => setLongTermSelectedTimeOption(option)}
                        activeOpacity={0.8}
                      >
                        <ThemedText style={[
                          styles.timeOptionText,
                          longTermSelectedTimeOption === option && styles.timeOptionTextSelected
                        ]}>
                          {option}
                        </ThemedText>
                      </TouchableOpacity>
                    ))}
                  </View>
                )}

                {longTermActiveContent === 'time' && (
                  <View style={styles.timeInfoContainer}>
                    <View style={styles.timeInfoBox}>
                      <ThemedText style={styles.timeInfoLabel}>开始</ThemedText>
                      <ThemedText style={styles.timeInfoValue}>7月27日 周日</ThemedText>
                      <ThemedText style={styles.timeInfoSubValue}>今天</ThemedText>
                    </View>
                    <View style={styles.timeInfoBox}>
                      <ThemedText style={styles.timeInfoLabel}>开始</ThemedText>
                      <ThemedText style={styles.timeInfoValue}>可设计划所需天数</ThemedText>
                      <ThemedText style={styles.timeInfoSubValue}>今天</ThemedText>
                    </View>
                  </View>
                )}

                {longTermActiveContent === 'remind' && (
                  <View style={styles.remindContainer}>
                    <ThemedText style={styles.remindText}>提醒设置</ThemedText>
                    <ThemedText style={styles.remindSubText}>设置提醒时间和方式</ThemedText>
                  </View>
                )}
              </Animated.View>
            )}
          </View>
        </View>
        </View>

        {/* 添加保存按钮 */}
        <GradientGlassButton
          title="保存"
          onPress={handleAddTask}
          style={styles.addTaskButton}
          gradientColors={['#FFEEDB', '#ffb87e']}
          borderColor="rgba(255, 194, 121, 0.5)"
          blurBackgroundColor="rgba(255, 238, 219, 0.3)"
          textColor="#7A3C10"
          borderRadius={16}
        />
      </ScrollView>
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFF8F3',
  },
  content: {
    paddingHorizontal: 16,
    paddingBottom: 20,
  },
  contentWrapper: {
    backgroundColor: '#FFD29B',
    borderRadius: 24,
    padding: 16,
    marginTop: 8,
    marginBottom: 8,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingTop: 50,
    paddingBottom: 20,
    position: 'relative',
  },
  backButton: {
    position: 'absolute',
    left: 0,
    top: 50, // 添加top值，与header的paddingTop保持一致
    zIndex: 1,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#222',
    textAlign: 'center',
    flex: 1,
  },
  goalCard: {
    marginBottom: 12,
  },
  goalTitleBox: {
    backgroundColor: '#FFEEDB',
    borderTopLeftRadius: 12,
    borderTopRightRadius: 12,
    borderBottomLeftRadius: 0,
    borderBottomRightRadius: 0,
    paddingHorizontal: 16,
    paddingVertical: 12,
    alignSelf: 'flex-start',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  goalTitle: {
    fontSize: 14,
    color: '#7A3C10',
    fontWeight: '600',
  },
  goalContentBox: {
    backgroundColor: '#FFEEDB',
    borderTopLeftRadius: 0,
    borderTopRightRadius: 12,
    borderBottomLeftRadius: 12,
    borderBottomRightRadius: 12,
    padding: 12,
    marginTop: -1,
    // 移除阴影以实现与goalTitleBox的无缝融合
    // shadowColor: '#000',
    // shadowOffset: { width: 0, height: 2 },
    // shadowOpacity: 0.1,
    // shadowRadius: 4,
    // elevation: 1,
  },
  goalItemRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  goalIcon: {
    width: 24,
    height: 24,
    marginRight: 12,
    resizeMode: 'contain',
  },
  goalText: {
    fontSize: 16,
    color: '#7A3C10',
    fontWeight: '500',
    flex: 1,
  },
  dividerLine: {
    height: 1,
    backgroundColor: '#7A3C10',
    marginTop: 2,
    marginBottom: 10,
    width: '80%',
    alignSelf: 'center',
  },
  iconRow: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginBottom: 8,
    paddingVertical: 4,
    gap: 60,
    alignItems: 'center',
  },
  iconButton: {
    alignItems: 'center',
    minWidth: 50,
    transform: [{ scale: 1 }],
  },
  functionIcon: {
    width: 32,
    height: 32,
    marginBottom: 4,
    resizeMode: 'contain',
  },
  iconLabel: {
    fontSize: 12,
    textAlign: 'center',
  },
  iconLabelActive: {
    color: '#7A3C10',
  },
  iconLabelInactive: {
    color: '#909090',
  },
  timeOptionsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 6,
    marginBottom: 8,
    marginTop: 4,
  },
  timeOption: {
    backgroundColor: 'rgba(255, 255, 255, 0.6)',
    borderRadius: 16,
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderWidth: 1,
    borderColor: '#909090',
  },
  timeOptionSelected: {
    backgroundColor: 'rgba(122, 60, 16, 0.1)',
    borderColor: '#7A3C10',
  },
  timeOptionText: {
    fontSize: 12,
    color: '#909090',
  },
  timeOptionTextSelected: {
    color: '#7A3C10',
    fontWeight: '600',
  },
  timeInfoContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    gap: 23,
    width: '90%',
    alignSelf: 'center',
    marginTop: 4,
  },
  timeInfoBox: {
    flex: 1,
    backgroundColor: '#FFFFFF',
    borderRadius: 8,
    padding: 10,
  },
  timeInfoLabel: {
    fontSize: 12,
    color: '#7A3C10',
    marginBottom: 4,
  },
  timeInfoValue: {
    fontSize: 14,
    color: '#7A3C10',
    fontWeight: '500',
    marginBottom: 2,
  },
  timeInfoSubValue: {
    fontSize: 12,
    color: '#7A3C10',
    opacity: 0.7,
  },
  addTaskButton: {
    width: '100%',
    height: 48,
    borderRadius: 16,
    marginTop: 16,
    marginBottom: 16,
  },
  remindContainer: {
    backgroundColor: '#FFFFFF',
    borderRadius: 8,
    padding: 12,
    marginTop: 8,
    alignItems: 'center',
  },
  remindText: {
    fontSize: 14,
    color: '#7A3C10',
    fontWeight: '500',
    marginBottom: 4,
  },
  remindSubText: {
    fontSize: 12,
    color: '#909090',
  },
  animatedContainer: {
    overflow: 'hidden',
  },
});
