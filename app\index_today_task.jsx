import React from 'react';
import { StyleSheet, View, Image, ScrollView, TouchableOpacity } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { router } from 'expo-router';
import { Colors } from '../constants/Colors';
import ThemedText from '../components/ThemedText';
import ThemedView from '../components/ThemedView';
import { Ionicons } from '@expo/vector-icons';

const ArrowImg = require('../assets/Plan_image/Arrows.png');
const AiImg = require('../assets/dogStandingOnPodium.png');

export default function IndexTodayTask() {
  const handleBack = () => {
    router.back();
  };

  return (
    <ThemedView style={styles.container}>
      <ScrollView contentContainerStyle={styles.content} showsVerticalScrollIndicator={false}>
        {/* 顶部导航 */}
        <View style={styles.header}>
          <TouchableOpacity onPress={handleBack} style={styles.backButton}>
            <Ionicons name="chevron-back" size={28} color="#222" />
          </TouchableOpacity>
          <ThemedText style={styles.headerTitle}>今日任务</ThemedText>
          <View style={styles.headerRight} />
        </View>

        {/* 今日任务内容 */}
        <View style={styles.taskContent}>
          {/* 顶部用户信息 */}
          <View style={styles.userInfo}>
            <View style={styles.avatar} />
            <View style={styles.userDetails}>
              <View style={styles.userNameRow}>
                <ThemedText style={styles.userName}>Jessica</ThemedText>
                <View style={styles.userTags}>
                  <View style={styles.userTag}>
                    <ThemedText style={styles.userTagText}>大一学生</ThemedText>
                  </View>
                  <View style={styles.userTag}>
                    <ThemedText style={styles.userTagText}>英语爱好者</ThemedText>
                  </View>
                </View>
              </View>
              <ThemedText style={styles.userSignature}>个性签名：自由和快乐是应该记录的事情</ThemedText>
            </View>
          </View>
          

          
          {/* 统计区 */}
          <View style={styles.statsContainer}>
            {/* 累计打卡 */}
            <View style={styles.statItem}>
              <ThemedText style={styles.statLabel}>累计打卡</ThemedText>
              <View style={styles.statValueContainer}>
                <ThemedText style={styles.statValue}>7</ThemedText>
                <ThemedText style={styles.statUnit}>天</ThemedText>
              </View>
            </View>
            
            {/* 今天学习 */}
            <View style={styles.statItem}>
              <ThemedText style={styles.statLabel}>今天学习</ThemedText>
              <View style={styles.statValueContainer}>
                <ThemedText style={styles.statValue}>36</ThemedText>
                <ThemedText style={styles.statUnit}>分</ThemedText>
              </View>
            </View>
            
            {/* 时长排名超过 */}
            <View style={styles.statItem}>
              <ThemedText style={styles.statLabel}>时长排名超过</ThemedText>
              <View style={styles.statValueContainer}>
                <ThemedText style={styles.statValue}>80</ThemedText>
                <ThemedText style={styles.statUnit}>％的同学</ThemedText>
              </View>
            </View>
          </View>

          {/* 已获得成就 */}
          <View style={[styles.achievementCard, { backgroundColor: '#FFE2BD' }]}>
            <View style={styles.cardContent}>
              <View style={styles.cardHeader}>
                <ThemedText style={styles.cardTitle}>已获得成就</ThemedText>
                <Image source={ArrowImg} style={styles.arrowIcon} />
              </View>
              
              <View style={styles.achievementTags}>
                <View style={styles.achievementTag}>
                  <ThemedText style={styles.achievementTagText}>七天连续打卡</ThemedText>
                </View>
                <View style={styles.achievementTag}>
                  <ThemedText style={styles.achievementTagText}>口语打卡全勤</ThemedText>
                </View>
                <View style={styles.achievementTag}>
                  <ThemedText style={styles.achievementTagText}>任务完成率90%+</ThemedText>
                </View>
              </View>

              <View style={styles.cardHeader}>
                <ThemedText style={styles.cardTitle}>已完成任务</ThemedText>
                <Image source={ArrowImg} style={styles.arrowIcon} />
              </View>
              
              <View style={styles.completedTask}>
                <View style={styles.checkIcon}>
                  <ThemedText style={styles.checkText}>✓</ThemedText>
                </View>
                <ThemedText style={styles.taskText}>一篇阅读理解</ThemedText>
              </View>
            </View>
          </View>

          {/* 待完成任务 */}
          <View style={styles.taskCard}>
            <LinearGradient
              colors={['#E5F5FF', '#FFE2BD']}
              start={{ x: 0, y: 0 }}
              end={{ x: 0, y: 1 }}
              style={styles.taskCardGradient}
            >
              <View style={styles.cardContent}>
              <View style={styles.cardHeader}>
                <ThemedText style={styles.cardTitle}>待完成任务</ThemedText>
                <View style={styles.countdownContainer}>
                  <ThemedText style={styles.countdownText}>倒计时：12h</ThemedText>
                  <Image source={ArrowImg} style={styles.arrowIcon} />
                </View>
              </View>
              
              {/* 任务列表 */}
              <View style={styles.taskItem}>
                <View style={styles.taskCheckbox} />
                <ThemedText style={styles.taskText}>背30个单词</ThemedText>
              </View>

              <View style={styles.taskItem}>
                <View style={styles.taskCheckbox} />
                <ThemedText style={styles.taskText}>一篇口语跟读</ThemedText>
              </View>

              <View style={styles.taskItem}>
                <View style={styles.taskCheckbox} />
                <ThemedText style={styles.taskText}>看教学视频</ThemedText>
              </View>
              
              {/* 进度条 */}
              <View style={styles.progressContainer}>
                <ThemedText style={styles.progressLabel}>完成进度</ThemedText>
                <View style={styles.progressBar}>
                  <View style={styles.progressFill} />
                </View>
                <ThemedText style={styles.progressText}>36%</ThemedText>
              </View>
            </View>
            </LinearGradient>
          </View>

          {/* 添加任务 */}
          <TouchableOpacity style={[styles.addTaskCard, { backgroundColor: '#FFE2BD' }]}>
            <ThemedText style={styles.addTaskText}>+ 添加任务</ThemedText>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFF8F3',
  },
  content: {
    paddingBottom: 20,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingTop: 60,
    paddingBottom: 20,
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#222',
  },
  headerRight: {
    width: 44,
  },
  taskContent: {
    paddingHorizontal: 16,
  },
  userInfo: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginTop: 8,
    marginBottom: 16,
  },
  avatar: {
    width: 56,
    height: 56,
    borderRadius: 28,
    backgroundColor: '#bbb',
    marginRight: 16,
  },
  userDetails: {
    flex: 1,
  },
  userNameRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  userName: {
    fontSize: 22,
    color: '#222',
    fontWeight: 'bold',
    marginRight: 12,
  },
  userTags: {
    flexDirection: 'row',
    gap: 6,
  },
  userTag: {
    backgroundColor: '#FFD29B',
    borderRadius: 10,
    paddingHorizontal: 8,
    paddingVertical: 2,
  },
  userTagText: {
    fontSize: 10,
    color: '#8B4513',
    fontWeight: '500',
  },
  userSignature: {
    fontSize: 12,
    color: '#666',
    lineHeight: 16,
  },
  divider: {
    height: 1,
    backgroundColor: '#bbb',
    marginBottom: 8,
  },
  statsContainer: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  statItem: {
    alignItems: 'center',
    flex: 1,
  },
  statLabel: {
    fontSize: 14,
    color: '#444',
    marginBottom: 2,
  },
  statValueContainer: {
    flexDirection: 'row',
    alignItems: 'flex-end',
  },
  statValue: {
    fontSize: 28,
    color: '#222',
    fontWeight: 'bold',
  },
  statUnit: {
    fontSize: 15,
    color: '#222',
    marginLeft: 2,
    marginBottom: 2,
  },
  achievementCard: {
    borderRadius: 16,
    padding: 16,
    marginTop: 12,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  taskCard: {
    borderRadius: 16,
    marginTop: 12,
    marginBottom: 12,
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  taskCardGradient: {
    padding: 16,
    borderRadius: 16,
  },
  cardContent: {
    flex: 1,
  },
  cardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  cardTitle: {
    fontSize: 16,
    color: '#222',
    fontWeight: 'bold',
  },
  arrowIcon: {
    width: 24,
    height: 24,
    resizeMode: 'contain',
  },
  achievementTags: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
    marginBottom: 8,
  },
  achievementTag: {
    backgroundColor: '#FFD29B',
    borderRadius: 12,
    paddingHorizontal: 12,
    paddingVertical: 6,
  },
  achievementTagText: {
    fontSize: 13,
    color: '#8B4513',
    fontWeight: '500',
  },
  sectionDivider: {
    height: 1,
    backgroundColor: '#ddd',
    marginVertical: 8,
  },
  completedTask: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  checkIcon: {
    width: 22,
    height: 22,
    borderRadius: 11,
    backgroundColor: '#FF8C42',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 8,
  },
  checkText: {
    fontSize: 16,
    color: '#fff',
  },
  countdownContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  countdownText: {
    fontSize: 15,
    color: '#444',
    marginRight: 4,
  },
  taskItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  taskCheckbox: {
    width: 22,
    height: 22,
    borderRadius: 11,
    borderWidth: 1,
    borderColor: '#bbb',
    backgroundColor: '#fff',
    marginRight: 8,
  },
  taskText: {
    fontSize: 15,
    color: '#444',
  },
  taskDivider: {
    height: 1,
    backgroundColor: '#ccc',
    marginBottom: 8,
    marginLeft: 30,
  },
  progressContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 12,
  },
  progressLabel: {
    fontSize: 15,
    color: '#444',
    marginRight: 8,
  },
  progressBar: {
    flex: 1,
    height: 12,
    borderRadius: 6,
    backgroundColor: '#E0E0E0',
    overflow: 'hidden',
  },
  progressFill: {
    width: '36%',
    height: '100%',
    backgroundColor: '#FF8C42',
    borderRadius: 6,
  },
  progressText: {
    fontSize: 13,
    color: '#444',
    marginLeft: 8,
  },
  addTaskCard: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 16,
    paddingHorizontal: 15,
    paddingVertical: 16,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  addTaskText: {
    fontSize: 16,
    color: '#888',
  },
  addTaskIcon: {
    fontSize: 28,
    color: '#888',
  },
});
