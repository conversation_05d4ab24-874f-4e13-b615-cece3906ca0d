import React, { useRef, useState, useEffect, useCallback } from 'react';
import { StyleSheet, View, TouchableOpacity, Dimensions, Alert, Image } from 'react-native';
import { router } from 'expo-router';
import apiServices from '../lib/apiServices';

import AsyncStorage from '@react-native-async-storage/async-storage';
import { LinearGradient } from 'expo-linear-gradient';
import ThemedText from '../components/ThemedText';
import GradientGlassButton from '../components/GradientGlassButton';
import { useUser } from '../hooks/useUser';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withSpring,
  withTiming,
  withDelay,
  useAnimatedGestureHandler,
  cancelAnimation,
  Easing,
  useFrameCallback,
  withRepeat,
  withSequence
} from 'react-native-reanimated';
import { PanGestureHandler } from 'react-native-gesture-handler';

const { width, height } = Dimensions.get('window');

// 优化的物理引擎配置
const PHYSICS_CONFIG = {
  gravity: 0.4,
  bounce: 0.4, // 降低弹性，减少摆动
  friction: 0.85, // 大幅增加摩擦力
  airResistance: 0.99,
  minVelocity: 0.05, // 更严格的最小速度阈值
  maxVelocity: 8,
  collisionThreshold: 5,//碰撞阈值
  groundFriction: 0.9,
  stackingForce: 0.5,//堆叠力
  separationDistance: -32,//分离距离
};

// 步骤数据配置
const steps = [
  {
    id: 1,
    title: "Step1:选择你当前的学习阶段 与核心目标",
    subtitle: "为了给您提供个性化服务,我们将为您进行分类选择",
    options: [
      { id: '1', text: '学科知识点', color: '#FFA087' },
      { id: '2', text: '考研', color: '#FFA087' },
      { id: '3', text: '在校大学生', color: '#BBE2BF' },
      { id: '4', text: '考试', color: '#BBE2BF' },
      { id: '5', text: '职场提升', color: '#9FD4FF' },
      { id: '6', text: '项目实践', color: '#9FD4FF' },
      { id: '7', text: '考职业证书', color: '#FFC885' },
      { id: '8', text: '技能提升', color: '#FFC885' },
    ]
  },
  {
    id: 2,
    title: "Step2:你更倾向于哪种学习方式?",
    subtitle: "为了给您提供个性化服务,我们将为您进行分类选择",
    options: [
      { id: '9', text: '小组讨论', color: '#FFA087' },
      { id: '10', text: '视频课程', color: '#FFA087' },
      { id: '11', text: '思维导图', color: '#BBE2BF' },
      { id: '12', text: '互动练习', color: '#BBE2BF' },
      { id: '13', text: '时间管理', color: '#9FD4FF' },
      { id: '14', text: '安静环境学习', color: '#9FD4FF' },
      { id: '15', text: '文字阅读', color: '#FFC885' },
    ]
  },
  {
    id: 3,
    title: "Step3:什么因素最制约你的学习 或职业发展?",
    subtitle: "为了给您提供个性化服务,我们将为您进行分类选择",
    options: [
      { id: '16', text: '职业方向模糊', color: '#FFA087' },
      { id: '17', text: '外界因素', color: '#FFA087' },
      { id: '18', text: '知识点难懂', color: '#FFA087' },
      { id: '19', text: '知识体系零散', color: '#BBE2BF' },
      { id: '20', text: '缺乏资源', color: '#BBE2BF' },
      { id: '21', text: '缺乏动力', color: '#BBE2BF' },
      { id: '22', text: '缺乏学习氛围', color: '#9FD4FF' },
      { id: '23', text: '时间碎片化', color: '#9FD4FF' },
      { id: '24', text: '缺乏人脉', color: '#FFC885' },
      { id: '25', text: '效率不高', color: '#FFC885' },
      { id: '26', text: '拖延', color: '#FFC885' },
    ]
  },
  {
    id: 4,
    title: "Step4:如果要提升自己,你觉得现在最大的挑战是什么?",
    subtitle: "为了给您提供个性化服务,我们将为您进行分类选择",
    options: [
      { id: '27', text: '精力分散', color: '#FFA087' },
      { id: '28', text: '目标太杂', color: '#FFA087' },
      { id: '29', text: '容易被外界影响', color: '#BBE2BF' },
      { id: '30', text: '没有实践平台', color: '#BBE2BF' },
      { id: '31', text: '基础知识薄弱', color: '#9FD4FF' },
      { id: '32', text: '专业技能不足', color: '#9FD4FF' },
      { id: '33', text: '缺乏优质资源', color: '#FFC885' },
    ]
  },
  {
    id: 5,
    title: "Step5:你希望通过AI学习搭子获得哪些具体的帮助?",
    subtitle: "为了给您提供个性化服务,我们将为您进行分类选择",
    options: [
      { id: '34', text: '进度监督', color: '#FFA087' },
      { id: '35', text: '职业衔接与能力匹配', color: '#FFA087' },
      { id: '36', text: '实践指导', color: '#BBE2BF' },
      { id: '37', text: '问题解答', color: '#BBE2BF' },
      { id: '38', text: '习惯养成', color: '#9FD4FF' },
      { id: '39', text: '规划制定', color: '#9FD4FF' },
      { id: '40', text: '技能提升', color: '#FFC885' },
      { id: '41', text: '个性化支持', color: '#FFC885' },
    ]
  }
];

// 碰撞检测工具函数
const checkCollision = (x1, y1, w1, h1, x2, y2, w2, h2) => {
  'worklet';
  return (
    x1 < x2 + w2 + PHYSICS_CONFIG.separationDistance &&
    x1 + w1 + PHYSICS_CONFIG.separationDistance > x2 &&
    y1 < y2 + h2 + PHYSICS_CONFIG.separationDistance &&
    y1 + h1 + PHYSICS_CONFIG.separationDistance > y2
  );
};

// 计算两点距离
const getDistance = (x1, y1, x2, y2) => {
  'worklet';
  return Math.sqrt((x2 - x1) ** 2 + (y2 - y1) ** 2);
};

// 优化的物理标签组件
const PhysicsTag = React.memo(({
  option,
  index,
  isSelected,
  onPress,
  tagRef,
  allTagRefs,
  tagWidth,
  tagHeight,
  stepId
}) => {
  // 边界计算
  const headerHeight = 50 + 20 + 20;
  const titleHeight = 80;
  const progressHeight = 30;
  const hintHeight = 60;
  const buttonHeight = 230;

  const physicsAreaTop = headerHeight + titleHeight + progressHeight + hintHeight;
  const physicsAreaBottom = height - buttonHeight;

  const bounds = {
    top: physicsAreaTop - 250,
    bottom: physicsAreaBottom - tagHeight - 300,
    left: 40,
    right: width - tagWidth - 40
  };

  // 物理属性
  const x = tagRef.x;
  const y = tagRef.y;
  const vx = tagRef.vx;
  const vy = tagRef.vy;

  const scale = useSharedValue(0.8);
  const opacity = useSharedValue(0);
  const hoverOffset = useSharedValue(0);
  const hovering = useSharedValue(false);

  // 状态跟踪
  const isDragging = tagRef.isDragging;
  const isGrounded = useSharedValue(false);
  const isStable = useSharedValue(false);
  const lastUpdateTime = useSharedValue(Date.now());
  const isActive = useSharedValue(true);
  const stabilityTimer = useSharedValue(0);

  // 标签唯一ID
  const tagId = useSharedValue(index);

  // 更新全局位置管理
  useEffect(() => {
    // 在 worklet 中更新 tagRefs 的值
    'worklet';
    tagRef.x.value = Math.random() * (bounds.right - bounds.left) + bounds.left;
    tagRef.y.value = bounds.top - 100 - index * 20;
    tagRef.vx.value = (Math.random() - 0.5) * 2;
    tagRef.vy.value = Math.random() * 1 + 0.5;
    // tagRef.width = tagWidth; // 移除此行
    // tagRef.height = tagHeight; // 移除此行

    return () => {
      // 在 worklet 中重置 tagRefs 的值
      'worklet';
      tagRef.x.value = 0;
      tagRef.y.value = 0;
      tagRef.vx.value = 0;
      tagRef.vy.value = 0;
      // tagRef.width = 0; // 移除此行
      // tagRef.height = 0; // 移除此行
    };
  }, [index, tagWidth, tagHeight]);

  // 标签间碰撞检测和处理
  const handleTagCollisions = () => {
    'worklet';
    if (!isActive.value || isDragging.value) return;

    for (let i = 0; i < allTagRefs.length; i++) {
      const otherTag = allTagRefs[i];
      if (!otherTag || i === index || otherTag.isDragging.value) continue;

      const dx = x.value - otherTag.x.value;
      const dy = y.value - otherTag.y.value;
      const distance = Math.sqrt(dx * dx + dy * dy);
      const minDistance = (tagWidth + otherTag.width) / 2 + PHYSICS_CONFIG.separationDistance;

      if (distance < minDistance && distance > 0) {
        // 计算重叠深度
        const overlap = minDistance - distance;
        const separationForce = overlap * PHYSICS_CONFIG.stackingForce;

        // 归一化方向向量
        const normalX = dx / distance;
        const normalY = dy / distance;

        // 分离标签
        const separationX = normalX * separationForce * 0.5;
        const separationY = normalY * separationForce * 0.5;

        x.value += separationX;
        y.value += separationY;

        // 应用反弹速度
        const relativeVelX = vx.value - otherTag.vx.value;
        const relativeVelY = vy.value - otherTag.vy.value;
        const relativeVelInNormal = relativeVelX * normalX + relativeVelY * normalY;

        if (relativeVelInNormal < 0) {
          const impulse = relativeVelInNormal * PHYSICS_CONFIG.bounce;
          vx.value -= impulse * normalX * 0.5;
          vy.value -= impulse * normalY * 0.5;
        }

        // 重置稳定性
        isStable.value = false;
        stabilityTimer.value = 0;
      }
    }
  };

  // 优化的物理更新循环
  const frameCallback = useFrameCallback((frameInfo) => {
    'worklet';
    if (!isActive.value || isDragging.value) return;

    const now = Date.now();
    const deltaTime = Math.min((now - lastUpdateTime.value) / 16.67, 2);
    lastUpdateTime.value = now;

    let currentX = x.value;
    let currentY = y.value;
    let currentVX = vx.value;
    let currentVY = vy.value;

    // 应用重力
    if (!isGrounded.value) {
      currentVY += PHYSICS_CONFIG.gravity * deltaTime;
    }

    // 应用空气阻力
    currentVX *= Math.pow(PHYSICS_CONFIG.airResistance, deltaTime);
    currentVY *= Math.pow(PHYSICS_CONFIG.airResistance, deltaTime);

    // 更新位置
    currentX += currentVX * deltaTime;
    currentY += currentVY * deltaTime;

    // 边界碰撞检测
    let wasGrounded = isGrounded.value;
    isGrounded.value = false;

    // 底部碰撞
    if (currentY >= bounds.bottom) {
      currentY = bounds.bottom;
      if (Math.abs(currentVY) > PHYSICS_CONFIG.minVelocity) {
        currentVY = -Math.abs(currentVY) * PHYSICS_CONFIG.bounce;
        bounced = true;
      } else {
        currentVY = 0;
        isGrounded.value = true;
      }
      // 地面摩擦
      if (isGrounded.value) {
        currentVX *= PHYSICS_CONFIG.groundFriction;
      }
    }

    // 顶部碰撞
    if (currentY <= bounds.top) {
      currentY = bounds.top;
      currentVY = Math.abs(currentVY) * PHYSICS_CONFIG.bounce;
      bounced = true;
    }

    // 左右边界碰撞
    if (currentX <= bounds.left) {
      currentX = bounds.left;
      currentVX = Math.abs(currentVX) * PHYSICS_CONFIG.bounce;
      bounced = true;
    } else if (currentX >= bounds.right) {
      currentX = bounds.right;
      currentVX = -Math.abs(currentVX) * PHYSICS_CONFIG.bounce;
      bounced = true;
    }

    // 限制速度
    const speed = Math.sqrt(currentVX * currentVX + currentVY * currentVY);
    if (speed > PHYSICS_CONFIG.maxVelocity) {
      currentVX = (currentVX / speed) * PHYSICS_CONFIG.maxVelocity;
      currentVY = (currentVY / speed) * PHYSICS_CONFIG.maxVelocity;
    }

    // 检查稳定性
    if (speed < PHYSICS_CONFIG.minVelocity && isGrounded.value) {
      stabilityTimer.value += deltaTime;
      if (stabilityTimer.value > 30) { // 0.5秒稳定后停止
        currentVX = 0;
        currentVY = 0;
        isStable.value = true;

      }
    } else {
      stabilityTimer.value = 0;
      isStable.value = false;
    }



    // 应用值
    x.value = currentX;
    y.value = currentY;
    vx.value = currentVX;
    vy.value = currentVY;

    // 处理标签间碰撞
    handleTagCollisions();

    // 悬停动画逻辑
    if (isGrounded.value && isStable.value && !hovering.value) {
      hovering.value = true;
      hoverOffset.value = withRepeat(
        withSequence(
          withTiming(-6, { duration: 400 }),
          withTiming(0, { duration: 400 })
        ),
        -1,
        true
      );
    } else if ((!isGrounded.value || !isStable.value) && hovering.value) {
      hovering.value = false;
      hoverOffset.value = withTiming(0, { duration: 200 });
    }
  }, true);

  // 入场动画
  useEffect(() => {
    const delay = index * 100;
    // 强制初始状态
    opacity.value = 0;
    scale.value = 0.8;
    // y.value 已在 tagRefs 初始化时设为天上

    // 下落动画
    y.value = withDelay(
      delay,
      withTiming(
        Math.random() * 100 + bounds.top + 30,
        { duration: 900 + Math.random() * 400, easing: Easing.bezier(0.4, 0.0, 0.2, 1) },
        () => {
          // 下落完成后再渐显
          opacity.value = withTiming(1, { duration: 250 });
        }
      )
    );
    scale.value = withDelay(delay, withSpring(1, { damping: 12 }));

    // 保险：如果动画中断，确保最终可见
    const timer = setTimeout(() => {
      if (opacity.value < 1) opacity.value = 1;
      if (scale.value < 1) scale.value = 1;
    }, delay + 1500);

    return () => {
      clearTimeout(timer);
      isActive.value = false;
      cancelAnimation(x);
      cancelAnimation(y);
      cancelAnimation(scale);
      cancelAnimation(opacity);
    };
  }, [stepId, index]);

  // 选中状态动画
  useEffect(() => {
    if (isSelected) {
      scale.value = withSpring(1.15, { damping: 8 });
    } else {
      scale.value = withSpring(1, { damping: 8 });
    }
  }, [isSelected]);

  // 优化的手势处理
  const gestureHandler = useAnimatedGestureHandler({
    onStart: (event, context) => {
      'worklet';
      isDragging.value = true;
      context.startX = x.value;
      context.startY = y.value;
      scale.value = withSpring(1.3);

      // 停止物理运动
      vx.value = 0;
      vy.value = 0;
      isStable.value = false;
      stabilityTimer.value = 0;
    },
    onActive: (event, context) => {
      'worklet';
      const newX = Math.max(bounds.left, Math.min(bounds.right, context.startX + event.translationX));
      const newY = Math.max(bounds.top, Math.min(bounds.bottom, context.startY + event.translationY));

      x.value = newX;
      y.value = newY;
    },
    onEnd: (event) => {
      'worklet';
      isDragging.value = false;
      scale.value = withSpring(isSelected ? 1.15 : 1);

      // 应用释放时的速度，但更温和
      vx.value = Math.max(-PHYSICS_CONFIG.maxVelocity * 0.3,
        Math.min(PHYSICS_CONFIG.maxVelocity * 0.3, event.velocityX * 0.0005));
      vy.value = Math.max(-PHYSICS_CONFIG.maxVelocity * 0.3,
        Math.min(PHYSICS_CONFIG.maxVelocity * 0.3, event.velocityY * 0.0005));

      isStable.value = false;
      stabilityTimer.value = 0;
    }
  });

  const animatedStyle = useAnimatedStyle(() => {
    return {
      transform: [
        { translateX: x.value },
        { translateY: y.value + hoverOffset.value },
        { scale: scale.value }
      ],
      opacity: opacity.value,
    };
  });

  const handlePress = useCallback(() => {
    // 温和的点击反馈
    scale.value = withSpring(1.4, { damping: 8 }, () => {
      'worklet';
      scale.value = withSpring(isSelected ? 1.15 : 1, { damping: 8 });
    });

    // 轻微的物理反应
    if (!isDragging.value) {
      vx.value = (Math.random() - 0.5) * 1.5;
      vy.value = -Math.random() * 1 - 0.5;
      isStable.value = false;
      stabilityTimer.value = 0;
    }
    // 关键修改：调用父组件传递的 onPress 并传入当前 option.id
    onPress(option.id);
  }, [onPress, isSelected, option.id]);

  return (
    <PanGestureHandler onGestureEvent={gestureHandler}>
      <Animated.View style={[styles.physicsTagContainer, animatedStyle]}>
        <TouchableOpacity
          style={[
            styles.physicsTag,
            { backgroundColor: option.color, width: tagWidth, height: tagHeight, paddingHorizontal: 4, paddingVertical: 4 },
            isSelected && styles.physicsTagSelected
          ]}
          onPress={handlePress}
          activeOpacity={0.8}
        >
          <ThemedText
            style={[
              styles.physicsTagText,
              isSelected && styles.physicsTagTextSelected,
              { textAlign: 'center' }
            ]}
            numberOfLines={2}
            ellipsizeMode="tail"
          >
            {option.text}
          </ThemedText>
          {isSelected && (
            <Animated.View style={styles.selectedIndicator}>
              <ThemedText style={styles.selectedIcon}>✓</ThemedText>
            </Animated.View>
          )}
        </TouchableOpacity>
      </Animated.View>
    </PanGestureHandler>
  );
});

export default function ProfileSetup() {
  const [currentStep, setCurrentStep] = useState(0);
  const [selectedOptions, setSelectedOptions] = useState({});
  const { updateProfileSetupStatus, user, logout } = useUser();
  const currentStepData = steps[currentStep];

  // useRef 初始化 tagRefsAllSteps，x/y 直接设为天上，初次渲染不会闪现
  const tagRefsAllSteps = useRef(
    steps.map((step, stepIdx) =>
      step.options.map((option, index) => ({
        x: useSharedValue(Math.random() * (width - 140) + 20),
        y: useSharedValue(-200 - index * 40), // 初始在天上
        vx: useSharedValue(0),
        vy: useSharedValue(0),
        isDragging: useSharedValue(false),
        width: Math.max(120, Math.min(260, option.text.length * 12 + 40)), // 增大宽度范围
        height: 55, // 增高
      }))
    )
  );
  const tagRefs = tagRefsAllSteps.current[currentStep];

  // 步骤切换时重置 tagRefs（只重置物理量，不重置 y/x 的初始天上位置）
  useEffect(() => {
    tagRefs.forEach(ref => {
      // 不重置 x/y，保持初始天上位置
      ref.vx.value = 0;
      ref.vy.value = 0;
      ref.isDragging.value = false;
    });
  }, [currentStep, tagRefs]);

  const handleOptionSelect = useCallback((optionId) => {
    setSelectedOptions(prev => {
      const currentStepId = currentStepData.id;
      const currentSelections = prev[currentStepId] || [];
      const newSelections = currentSelections.includes(optionId)
        ? currentSelections.filter(id => id !== optionId)
        : [...currentSelections, optionId];

      if (newSelections.length > 5) {
        Alert.alert('提示', '每步最多选择5个选项');
        return prev;
      }

      const newState = {
        ...prev,
        [currentStepId]: newSelections
      };
      return newState;
    });
  }, [currentStepData.id]);

  const handleContinue = async () => {
    if (currentStep < steps.length - 1) {
      // 前4次点击继续，不推送，直接进入下一步
      setCurrentStep(currentStep + 1);
    } else {
      // 最后一步点击进入时，推送所有已选id给后端
      await apiServices.auth.executeWithLogin(
        async (user) => {
          const allSelectedIds = Object.values(selectedOptions).flat();
          console.log('最后一步推送给后端的所有已选ID:', allSelectedIds);

          // 保存标签并生成AI画像
          const aiProfile = await apiServices.profile.saveUserTagAndGenerateProfile(selectedOptions);

          // 保存到本地存储
          await updateProfileSetupStatus(true);
          await AsyncStorage.setItem("userProfileData", JSON.stringify({
            selections: selectedOptions,
            aiProfile,
            timestamp: new Date().toISOString(),
          }));

          console.log('用户标签保存成功，设置第一次登录标记并跳转到dashboard');
          // 设置第一次登录标记，让GuestOnly组件处理跳转到plans页面
          await AsyncStorage.setItem('isFirstLogin', 'true');
          router.replace('/(dashboard)');
        },
        '保存用户标签',
        (error) => {
          // 需要登录时的回调
          alert(error.message);
          router.replace('/login');
        }
      ).catch((error) => {
        console.error('保存失败:', error);
        alert('保存失败: ' + error.message);
      });
    }
  };

  const handleBack = async () => {
    console.log('🔙 handleBack 被调用，当前步骤:', currentStep);

    if (currentStep === 0) {
      // 第一步：直接跳转到登录页面
      console.log('🔙 第一步，强制跳转到登录页面');

      // 先清除用户登录状态，然后跳转到登录页面
      console.log('🔙 清除用户登录状态并跳转到登录页面');

      try {
        // 清除用户登录状态
        await logout();
        console.log('🔙 用户登录状态已清除');

        // 跳转到登录页面
        router.replace('/(auth)/login');

      } catch (error) {
        console.error('🔙 清除登录状态失败:', error);
        // 备用方案：直接跳转到welcome页面
        if (typeof window !== 'undefined') {
          console.log('🔙 使用备用方案：跳转到welcome');
          window.location.href = `${window.location.origin}/welcome`;
        }
      }
    } else {
      // 其他步骤：返回上一步
      console.log('🔙 返回上一步，从步骤', currentStep, '到步骤', currentStep - 1);
      setCurrentStep(currentStep - 1);
    }
  };

  const isOptionSelected = useCallback((optionId) => {
    return (selectedOptions[currentStepData.id] || []).includes(optionId);
  }, [selectedOptions, currentStepData.id]);

  const canContinue = () => {
    const hasSelections = (selectedOptions[currentStepData.id] || []).length > 0;
    console.log('canContinue check:', {
      currentStepId: currentStepData.id,
      selectedOptions: selectedOptions[currentStepData.id] || [],
      hasSelections
    });
    return hasSelections;
  };

  return (
    <LinearGradient
      colors={['#FCF1E4', '#F5F5F5']}
      style={styles.container}
    >
      {/* 顶部状态栏 */}
      <View style={styles.header}>
        <TouchableOpacity
          onPress={() => {
            console.log('🔙 返回按钮被点击！');
            handleBack();
          }}
          style={styles.backButton}
          activeOpacity={0.7}
          hitSlop={{ top: 15, bottom: 15, left: 15, right: 15 }}
        >
          <Image
            source={require('../assets/Arrows_left.png')}
            style={styles.backArrowImage}
            resizeMode="contain"
            tintColor="#000000"
          />
        </TouchableOpacity>
      </View>

      {/* 标题和说明 */}
      <View style={styles.titleContainer}>
        <ThemedText style={styles.title}>{currentStepData.title}</ThemedText>
        <ThemedText style={styles.subtitle}>{currentStepData.subtitle}</ThemedText>

        {/* 调试按钮 - 已注释，功能已迁移到profile-settings.jsx的退出登录按钮 */}
        {/*
        {__DEV__ && currentStep === 0 && (
          <View style={{ marginTop: 20, alignItems: 'center' }}>
            <TouchableOpacity
              onPress={() => {
                console.log('🧪 测试直接跳转到welcome');
                window.location.href = `${window.location.origin}/welcome`;
              }}
              style={{
                backgroundColor: '#4CAF50',
                padding: 10,
                borderRadius: 5,
                marginBottom: 10
              }}
            >
              <ThemedText style={{ color: 'white', fontSize: 12 }}>
                测试跳转到Welcome
              </ThemedText>
            </TouchableOpacity>

            <TouchableOpacity
              onPress={() => {
                console.log('🧪 测试直接跳转到登录');
                console.log('🧪 当前URL:', window.location.href);
                console.log('🧪 当前pathname:', window.location.pathname);

                // 尝试不同的路径格式
                const testPaths = [
                  `${window.location.origin}/(auth)/login`,
                  `${window.location.origin}/login`,
                  `${window.location.origin}/#/(auth)/login`,
                  `${window.location.origin}/#/login`
                ];

                console.log('🧪 测试路径:', testPaths);

                // 先尝试第一个路径
                window.location.href = testPaths[0];
              }}
              style={{
                backgroundColor: '#2196F3',
                padding: 10,
                borderRadius: 5
              }}
            >
              <ThemedText style={{ color: 'white', fontSize: 12 }}>
                测试跳转到登录
              </ThemedText>
            </TouchableOpacity>

            <TouchableOpacity
              onPress={() => {
                console.log('🧪 测试使用router跳转到登录');
                try {
                  router.replace('/(auth)/login');
                  console.log('🧪 router.replace 调用成功');
                } catch (error) {
                  console.error('🧪 router.replace 失败:', error);
                }
              }}
              style={{
                backgroundColor: '#FF9800',
                padding: 10,
                borderRadius: 5,
                marginTop: 10
              }}
            >
              <ThemedText style={{ color: 'white', fontSize: 12 }}>
                测试Router跳转
              </ThemedText>
            </TouchableOpacity>

            <TouchableOpacity
              onPress={async () => {
                console.log('🧪 测试清除登录状态');
                try {
                  await logout();
                  console.log('🧪 登录状态已清除');

                  // 清除后尝试跳转
                  setTimeout(() => {
                    router.replace('/(auth)/login');
                  }, 500);
                } catch (error) {
                  console.error('🧪 清除登录状态失败:', error);
                }
              }}
              style={{
                backgroundColor: '#E91E63',
                padding: 10,
                borderRadius: 5,
                marginTop: 10
              }}
            >
              <ThemedText style={{ color: 'white', fontSize: 12 }}>
                测试清除登录状态
              </ThemedText>
            </TouchableOpacity>
          </View>
        )}
        */}
      </View>

      {/* 进度条 */}
      <View style={styles.progressContainer}>
        {steps.map((step, index) => (
          <View
            key={step.id}
            style={[
              styles.progressBar,
              index <= currentStep ? styles.progressActive : styles.progressInactive
            ]}
          />
        ))}
      </View>

      {/* 物理标签区域 */}
      <View style={styles.physicsContainer}>

        {/* 物理边界提示 */}
        <View style={styles.boundaries}>
          <View style={[styles.boundary, styles.topBoundary]} />
          <View style={[styles.boundary, styles.leftBoundary]} />
          <View style={[styles.boundary, styles.rightBoundary]} />
          <View style={[styles.boundary, styles.bottomBoundary]} />
        </View>

        {/* 物理标签 */}
        {currentStepData.options.map((option, index) => {
          const tagRef = tagRefs[index];
          return (
            <PhysicsTag
              key={`${currentStepData.id}-${option.id}`}
              option={option}
              index={index}
              isSelected={isOptionSelected(option.id)}
              onPress={handleOptionSelect}
              tagRef={tagRef}
              allTagRefs={tagRefs}
              tagWidth={tagRef.width}
              tagHeight={tagRef.height}
              stepId={currentStepData.id}
            />
          );
        })}
      </View>

      {/* 底部按钮 */}
      <View style={styles.buttonContainer}>
        <GradientGlassButton
          title={currentStep === steps.length - 1 ? '进入' : '继续'}
          onPress={handleContinue}
          disabled={!canContinue()}
          borderRadius={12}
          style={[
            styles.continueButton,
            !canContinue() && styles.continueButtonDisabled
          ]}
        />
      </View>
    </LinearGradient>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingTop: 50,
    paddingHorizontal: 20,
    paddingBottom: 50,
    zIndex: 10,
  },
  backButton: {
    paddingTop: 10,
    paddingBottom: 10,
    paddingLeft: 5,
    paddingRight: 15,
    minWidth: 44,
    minHeight: 44,
    justifyContent: 'center',
    alignItems: 'flex-start',
  },
  backArrowImage: {
    width: 24,
    height: 24,
  },
  titleContainer: {
    paddingHorizontal: 20,
    marginBottom: 25,
    zIndex: 10,
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#7A3C10',
    marginBottom: 32,
    textAlign: 'center',
    lineHeight: 28,
    maxWidth: 270,
    alignSelf: 'center',
  },
  subtitle: {
    fontSize: 14,
    color: '#727272',
    textAlign: 'center',
    lineHeight: 20,
    marginBottom: 16,

  },
  progressContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 20,
    marginBottom: 25,
    zIndex: 10,
  },
  progressBar: {
    width: 55,
    height: 8,
    marginHorizontal: 4,
    borderRadius: 4,
  },
  progressActive: {
    backgroundColor: '#ECA56A',
  },
  progressInactive: {
    backgroundColor: '#E0E0E0',
  },
  physicsContainer: {
    flex: 1,
    position: 'relative',
    overflow: 'hidden',
  },

  boundaries: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: 1,
  },
  boundary: {
    position: 'absolute',
    // backgroundColor: 'rgba(255, 107, 53, 0.15)',
  },
  topBoundary: {
    top: 70, // 调整到正确的顶部边界位置
    left: 0,
    right: 0,
    height: 2,
  },
  bottomBoundary: {
    bottom: 130, // 调整到按钮上方的正确位置
    left: 0,
    right: 0,
    height: 2,
  },
  leftBoundary: {
    top: 70,
    bottom: 130,
    left: 20,
    width: 2,
  },
  rightBoundary: {
    top: 70,
    bottom: 130,
    right: 20,
    width: 2,
  },
  physicsTagContainer: {
    position: 'absolute',
    zIndex: 20,
  },
  physicsTag: {
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 25,
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 6,
    },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 10,
    borderWidth: 2,
    borderColor: 'transparent',
  },
  physicsTagSelected: {
    borderColor: '#333',
    shadowOpacity: 0.5,
    elevation: 15,
  },
  physicsTagText: {
    fontSize: 13,
    fontWeight: '600',
    color: '#333',
    textAlign: 'center',
  },
  physicsTagTextSelected: {
    color: '#333',
    fontWeight: 'bold',
  },
  selectedIndicator: {
    position: 'absolute',
    top: -8,
    right: -8,
    width: 22,
    height: 22,
    borderRadius: 11,
    backgroundColor: '#333',
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 4,
    elevation: 8,
  },
  selectedIcon: {
    color: '#FFF',
    fontSize: 12,
    fontWeight: 'bold',
  },
  buttonContainer: {
    paddingHorizontal: 40,
    paddingBottom: 60,
    zIndex: 10,
  },
  continueButton: {
    height: 50,
    marginTop: 0,
    marginBottom: 0,
    shadowColor: '#FFB87E',
    shadowOffset: {
      width: 0,
      height: 6,
    },
    shadowOpacity: 0.3,
    shadowRadius: 12,
    elevation: 8,
  },
  continueButtonDisabled: {
    opacity: 0.5,
    shadowColor: '#CCCCCC',
    shadowOpacity: 0.15,
    elevation: 4,
  },
});