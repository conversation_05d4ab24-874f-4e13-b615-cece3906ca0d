export const Colors = {
  primary: "#5849a7",
  warning: "#cc475a",
  // 新增的主题颜色 - 根据色卡更新
  background: "#FFF8F3", // 背景色 - 恢复原来的颜色
  button: "#FFC47B", // 按钮颜色 - 使用色卡中的橙黄色
  boldText: "#0000000", // 加粗字体颜色 - 使用色卡中的深橙色
  normalText: "#999999", // 正常字体颜色

  // 色卡中的其他颜色
  lightGreen: "#DBF1CD", // 浅绿色
  lightOrange: "#FFE7CD", // 浅橙色
  lightPurple: "#E2DCF1", // 浅紫色
  lightBlue: "#CDEAFA", // 浅蓝色
  darkRed: "#BE3A3A", // 深红色

  dark: {
    text: "#d4d4d4",
    title: "#fff",
    background: "#252231",
    navBackground: "#201e2b",
    iconColor: "#5951a5",
    iconColorFocused: "#fff",
    uiBackground: "#2f2b3d",
  },
  light: {
    text: "#625f72",
    title: "#201e2b",
    background: "#FFF8F3",
    navBackground: "#ffd1d1ff",
    iconColor: "#586477",
    iconColorFocused: "#201e2b",
    uiBackground: "#FFD29B",
  },
};