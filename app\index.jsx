import { StyleSheet } from 'react-native'
import { router } from 'expo-router'
import { useEffect } from 'react'
import AsyncStorage from '@react-native-async-storage/async-storage'
import { useUser } from '../hooks/useUser'

import ThemedView from "../components/ThemedView"
import ThemedText from "../components/ThemedText"

const Home = () => {
  const { user, authChecked } = useUser();

  useEffect(() => {
    if (!authChecked) {
      return;
    }

    console.log('🏠 WARNING: Root index page accessed! This should only happen when user directly visits root URL');
    console.log('🏠 If you see this during refresh, there is a routing issue');
    console.log('🏠 Current URL:', typeof window !== 'undefined' ? window.location.href : 'N/A');
    console.log('🏠 User state:', user ? (user.isGuest ? 'guest' : 'user') : 'none');

    // 简单重定向，不做复杂逻辑
    if (user) {
      console.log('👤 Redirecting logged in user to dashboard');
      router.replace('/(dashboard)');
    } else {
      console.log('🚪 Redirecting to welcome');
      router.replace('/welcome');
    }
  }, [user, authChecked]);

  // 开发时可以使用这个函数重置访问状态
  // const resetVisitedFlag = async () => {
  //   try {
  //     await AsyncStorage.removeItem('hasVisited');
  //     console.log('Visited flag reset');
  //   } catch (error) {
  //     console.error('Error resetting visited flag:', error);
  //   }
  // };

  // 如果还在检查认证状态，直接返回空视图，避免闪屏
  if (!authChecked) {
    return null;
  }

  // 这个页面不应该被显示，因为会自动重定向，返回空视图
  return null;
}

export default Home

const styles = StyleSheet.create({
  container: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  img: {
    marginVertical: 20
  },
  title: {
    fontWeight: 'bold',
    fontSize: 18,
  },
  link: {
    marginVertical: 10,
    borderBottomWidth: 1
  },
})