import { StyleSheet } from 'react-native'
import { router, useSegments } from 'expo-router'
import { useEffect, useState } from 'react'
import AsyncStorage from '@react-native-async-storage/async-storage'
import { useUser } from '../hooks/useUser'

import ThemedView from "../components/ThemedView"
import ThemedText from "../components/ThemedText"

const Home = () => {
  const { user, authChecked } = useUser();
  const [isFirstTime, setIsFirstTime] = useState(null);
  const segments = useSegments();

  useEffect(() => {
    const checkFirstTime = async () => {
      try {
        const hasVisited = await AsyncStorage.getItem('hasVisited');
        setIsFirstTime(!hasVisited);
      } catch (error) {
        console.error('Error checking first time visit:', error);
        setIsFirstTime(true);
      }
    };

    checkFirstTime();
  }, []);

  useEffect(() => {
    const handleNavigation = async () => {
      if (authChecked && isFirstTime !== null) {
        // 检查当前是否在根路径（index页面）
        const isOnIndexPage = segments.length === 0 || (segments.length === 1 && segments[0] === 'index');

        // 只有在index页面时才进行跳转
        if (isOnIndexPage) {
          if (isFirstTime) {
            // 第一次访问，设置标记并跳转
            try {
              await AsyncStorage.setItem('hasVisited', 'true');
            } catch (error) {
              console.error('Error setting visited flag:', error);
            }

            if (user) {
              // 已登录用户第一次进入，跳转到plans页面
              router.replace('/(dashboard)/plans?tab=plans');
            } else {
              // 未登录用户第一次进入，跳转到welcome页面
              router.replace('/welcome');
            }
          } else {
            // 不是第一次访问，但在根路径，跳转到合适的默认页面
            if (user) {
              // 已登录用户，跳转到dashboard首页
              router.replace('/(dashboard)');
            } else {
              // 未登录用户，跳转到welcome页面
              router.replace('/welcome');
            }
          }
        }
        // 如果不在根路径，不进行任何跳转，保持当前页面
      }
    };

    handleNavigation();
  }, [user, authChecked, isFirstTime, segments]);

  // 开发时可以使用这个函数重置访问状态
  // const resetVisitedFlag = async () => {
  //   try {
  //     await AsyncStorage.removeItem('hasVisited');
  //     console.log('Visited flag reset');
  //   } catch (error) {
  //     console.error('Error resetting visited flag:', error);
  //   }
  // };

  // 如果还在检查认证状态或第一次访问状态，直接返回空视图，避免闪屏
  if (!authChecked || isFirstTime === null) {
    return null;
  }

  // 这个页面不应该被显示，因为会自动重定向，返回空视图
  return null;
}

export default Home

const styles = StyleSheet.create({
  container: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  img: {
    marginVertical: 20
  },
  title: {
    fontWeight: 'bold',
    fontSize: 18,
  },
  link: {
    marginVertical: 10,
    borderBottomWidth: 1
  },
})