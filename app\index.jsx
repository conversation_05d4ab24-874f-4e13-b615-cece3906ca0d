import { StyleSheet } from 'react-native'
import { router } from 'expo-router'
import { useEffect } from 'react'
import AsyncStorage from '@react-native-async-storage/async-storage'
import { useUser } from '../hooks/useUser'

import ThemedView from "../components/ThemedView"
import ThemedText from "../components/ThemedText"

const Home = () => {
  const { user, authChecked } = useUser();

  useEffect(() => {
    const handleNavigation = async () => {
      if (!authChecked) {
        return;
      }

      console.log('🏠 Root index page accessed - user directly visited root');

      // 用户直接访问了根路径，重定向到合适的页面
      if (user) {
        console.log('👤 Logged in user at root → dashboard index');
        router.replace('/(dashboard)/?tab=index');
      } else {
        console.log('🚪 No user at root → welcome');
        router.replace('/welcome');
      }
    };

    handleNavigation();
  }, [user, authChecked]);

  // 开发时可以使用这个函数重置访问状态
  // const resetVisitedFlag = async () => {
  //   try {
  //     await AsyncStorage.removeItem('hasVisited');
  //     console.log('Visited flag reset');
  //   } catch (error) {
  //     console.error('Error resetting visited flag:', error);
  //   }
  // };

  // 如果还在检查认证状态，直接返回空视图，避免闪屏
  if (!authChecked) {
    return null;
  }

  // 这个页面不应该被显示，因为会自动重定向，返回空视图
  return null;
}

export default Home

const styles = StyleSheet.create({
  container: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  img: {
    marginVertical: 20
  },
  title: {
    fontWeight: 'bold',
    fontSize: 18,
  },
  link: {
    marginVertical: 10,
    borderBottomWidth: 1
  },
})