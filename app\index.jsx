import { StyleSheet } from 'react-native'
import { router } from 'expo-router'
import { useEffect, useState } from 'react'
import AsyncStorage from '@react-native-async-storage/async-storage'
import { useUser } from '../hooks/useUser'

import ThemedView from "../components/ThemedView"
import ThemedText from "../components/ThemedText"

const Home = () => {
  const { user, authChecked } = useUser();
  const [hasNavigated, setHasNavigated] = useState(false);

  useEffect(() => {
    const handleNavigation = async () => {
      // 防止重复导航
      if (hasNavigated || !authChecked) {
        return;
      }

      console.log('Index page navigation check:', {
        hasNavigated,
        authChecked,
        user: user ? (user.isGuest ? 'guest' : 'user') : 'none'
      });

      setHasNavigated(true); // 标记已经导航过

      // 检查是否是第一次访问
      try {
        const hasVisited = await AsyncStorage.getItem('hasVisited');
        const isFirstTime = !hasVisited;

        if (isFirstTime) {
          // 第一次访问，设置标记
          await AsyncStorage.setItem('hasVisited', 'true');
          console.log('First time visit detected');

          if (user) {
            if (user.isGuest) {
              console.log('First time guest user, redirecting to dashboard index');
              router.replace('/(dashboard)/?tab=index');
            } else {
              console.log('First time regular user, redirecting to plans');
              router.replace('/(dashboard)/plans?tab=plans');
            }
          } else {
            console.log('First time no user, redirecting to welcome');
            router.replace('/welcome');
          }
        } else {
          // 不是第一次访问
          console.log('Returning visit detected');

          if (user) {
            console.log('Returning user, redirecting to dashboard index');
            router.replace('/(dashboard)/?tab=index');
          } else {
            console.log('No user, redirecting to welcome');
            router.replace('/welcome');
          }
        }
      } catch (error) {
        console.error('Error in navigation logic:', error);
        // 出错时的默认行为
        if (user) {
          router.replace('/(dashboard)/?tab=index');
        } else {
          router.replace('/welcome');
        }
      }
    };

    handleNavigation();
  }, [user, authChecked, hasNavigated]);

  // 开发时可以使用这个函数重置访问状态
  // const resetVisitedFlag = async () => {
  //   try {
  //     await AsyncStorage.removeItem('hasVisited');
  //     console.log('Visited flag reset');
  //   } catch (error) {
  //     console.error('Error resetting visited flag:', error);
  //   }
  // };

  // 如果还在检查认证状态或第一次访问状态，直接返回空视图，避免闪屏
  if (!authChecked || isFirstTime === null) {
    return null;
  }

  // 这个页面不应该被显示，因为会自动重定向，返回空视图
  return null;
}

export default Home

const styles = StyleSheet.create({
  container: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  img: {
    marginVertical: 20
  },
  title: {
    fontWeight: 'bold',
    fontSize: 18,
  },
  link: {
    marginVertical: 10,
    borderBottomWidth: 1
  },
})