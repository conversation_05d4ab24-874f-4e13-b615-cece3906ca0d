import { StyleSheet } from 'react-native'
import { router } from 'expo-router'
import { useEffect, useState } from 'react'
import AsyncStorage from '@react-native-async-storage/async-storage'
import { useUser } from '../hooks/useUser'

import ThemedView from "../components/ThemedView"
import ThemedText from "../components/ThemedText"

const Home = () => {
  const { user, authChecked } = useUser();
  const [isFirstTime, setIsFirstTime] = useState(null);

  useEffect(() => {
    const checkFirstTime = async () => {
      try {
        const hasVisited = await AsyncStorage.getItem('hasVisited');
        setIsFirstTime(!hasVisited);
      } catch (error) {
        console.error('Error checking first time visit:', error);
        setIsFirstTime(true);
      }
    };

    checkFirstTime();
  }, []);

  useEffect(() => {
    const handleNavigation = async () => {
      if (authChecked && isFirstTime !== null) {
        if (isFirstTime) {
          // 第一次访问，设置标记并跳转
          try {
            await AsyncStorage.setItem('hasVisited', 'true');
          } catch (error) {
            console.error('Error setting visited flag:', error);
          }

          if (user) {
            // 已登录用户第一次进入，跳转到plans页面
            router.replace('/(dashboard)/plans?tab=plans');
          } else {
            // 未登录用户第一次进入，跳转到welcome页面
            router.replace('/welcome');
          }
        } else {
          // 不是第一次访问，保持在当前页面或跳转到合适的页面
          if (user) {
            // 已登录用户，跳转到dashboard首页
            router.replace('/(dashboard)');
          } else {
            // 未登录用户，跳转到welcome页面
            router.replace('/welcome');
          }
        }
      }
    };

    handleNavigation();
  }, [user, authChecked, isFirstTime]);

  // 开发时可以使用这个函数重置访问状态
  // const resetVisitedFlag = async () => {
  //   try {
  //     await AsyncStorage.removeItem('hasVisited');
  //     console.log('Visited flag reset');
  //   } catch (error) {
  //     console.error('Error resetting visited flag:', error);
  //   }
  // };

  // 如果还在检查认证状态或第一次访问状态，直接返回空视图，避免闪屏
  if (!authChecked || isFirstTime === null) {
    return null;
  }

  // 这个页面不应该被显示，因为会自动重定向，返回空视图
  return null;
}

export default Home

const styles = StyleSheet.create({
  container: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  img: {
    marginVertical: 20
  },
  title: {
    fontWeight: 'bold',
    fontSize: 18,
  },
  link: {
    marginVertical: 10,
    borderBottomWidth: 1
  },
})