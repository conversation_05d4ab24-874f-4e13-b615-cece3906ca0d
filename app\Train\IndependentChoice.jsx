import React from 'react';
import { View, StyleSheet } from 'react-native';
import ThemedView from '../../components/ThemedView';
import ThemedText from '../../components/ThemedText';

const IndependentChoice = () => {
  return (
    <ThemedView style={styles.container}>
      <ThemedText style={styles.title}>独立选择</ThemedText>
      <ThemedText style={styles.subtitle}>此页面正在开发中...</ThemedText>
    </ThemedView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 16,
  },
  subtitle: {
    fontSize: 16,
    textAlign: 'center',
  },
});

export default IndependentChoice;