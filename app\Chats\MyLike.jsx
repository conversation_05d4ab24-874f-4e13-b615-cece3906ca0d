import React from 'react';
import { StyleSheet, View, TouchableOpacity, Image, ScrollView, SafeAreaView } from 'react-native';
import { useRouter } from 'expo-router';
import ThemedText from '../../components/ThemedText';
import ThemedView from '../../components/ThemedView';
import { Colors } from '../../constants/Colors';

const MyLike = () => {
  const router = useRouter();

  const likeData = [
    {
      id: 1,
      type: 'user',
      name: '三三',
      message: '赞了你的帖子',
      time: '2025-05-14',
      avatar: require('../../assets/Community_image/AvatarOne.png'),
      postImage: require('../../assets/Community_image/AvatarTwo.png'),
      postTitle: '新发布的帖子没有关注-0',
      isRead: false
    },
    {
      id: 2,
      type: 'user',
      name: '灰狼',
      message: '赞了你的评论',
      time: '2025-05-13',
      avatar: require('../../assets/Community_image/AvatarThree.png'),
      postImage: require('../../assets/Community_image/AvatarFour.png'),
      postTitle: '学习心得分享',
      isRead: true
    },
    {
      id: 3,
      type: 'user',
      name: '小明',
      message: '赞了你的帖子',
      time: '2025-05-12',
      avatar: require('../../assets/Community_image/AvatarOne.png'),
      postImage: require('../../assets/Community_image/AvatarTwo.png'),
      postTitle: '今天的学习总结',
      isRead: true
    }
  ];

  return (

    <ThemedView style={styles.container}>
      {/* 顶部标题栏 */}
      <View style={styles.header}>
        <TouchableOpacity onPress={() => {
          console.log('Navigating back to community');
          if (router.canGoBack()) {
            router.back();
          } else {
            router.push('/(dashboard)/community');
          }
        }}>
          <Image source={require('../../assets/FrameTwo.png')} style={styles.backIcon} />
        </TouchableOpacity>
        <ThemedText style={styles.headerTitle}>的点赞</ThemedText>
        <TouchableOpacity>
          <Image source={require('../../assets/FrameFour.png')} style={styles.settingsIcon} />
        </TouchableOpacity>
      </View>


      {/* 点赞列表 */}
      <ScrollView style={styles.likeList} showsVerticalScrollIndicator={false}>
        {likeData.map((like) => (
          <TouchableOpacity key={like.id} style={[styles.likeItem, !like.isRead && styles.unreadLike]}>
            <View style={styles.likeHeader}>
              <Image source={like.avatar} style={styles.userAvatar} />
              <View style={styles.likeContent}>
                <View style={styles.likeInfo}>
                  <ThemedText style={styles.userName}>{like.name}</ThemedText>
                  <ThemedText style={styles.likeTime}>{like.time}</ThemedText>
                </View>
                <ThemedText style={styles.likeMessage}>{like.message}</ThemedText>
              </View>
            </View>
            <View style={styles.postPreview}>
              <Image source={like.postImage} style={styles.postImage} />
              <ThemedText style={styles.postTitle} numberOfLines={1}>
                {like.postTitle}
              </ThemedText>
            </View>
            {!like.isRead && <View style={styles.unreadDot} />}
          </TouchableOpacity>
        ))}
      </ScrollView>
    </ThemedView>

  );
};

const styles = StyleSheet.create({

  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingTop: 60,
    paddingBottom: 16,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  backIcon: {
    marginTop: 15,
    width: 24,
    height: 24,
    resizeMode: 'contain',
  },
  headerTitle: {
    marginTop: 20,
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.light.title,
  },
  settingsIcon: {
    marginTop: 15,
    width: 24,
    height: 24,
    resizeMode: 'contain',
  },
  likeList: {
    flex: 1,
    backgroundColor: '#fff',
  },
  likeItem: {
    paddingHorizontal: 16,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
    backgroundColor: '#fff',
    position: 'relative',
  },
  unreadLike: {
    backgroundColor: '#f8f9ff',
  },
  likeHeader: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  userAvatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
    marginRight: 12,
  },
  likeContent: {
    flex: 1,
  },
  likeInfo: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 4,
  },
  userName: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.light.title,
  },
  likeTime: {
    fontSize: 12,
    color: '#999',
  },
  likeMessage: {
    fontSize: 14,
    color: Colors.light.text,
    lineHeight: 20,
  },
  postPreview: {
    flexDirection: 'row',
    alignItems: 'center',
    marginLeft: 52,
    backgroundColor: '#f8f8f8',
    borderRadius: 8,
    padding: 8,
  },
  postImage: {
    width: 40,
    height: 40,
    borderRadius: 6,
    marginRight: 8,
    backgroundColor: '#e0e0e0',
  },
  postTitle: {
    flex: 1,
    fontSize: 14,
    color: Colors.light.text,
    lineHeight: 18,
  },
  unreadDot: {
    position: 'absolute',
    top: 16,
    right: 16,
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: '#FF6B6B',
  },
});

export default MyLike;