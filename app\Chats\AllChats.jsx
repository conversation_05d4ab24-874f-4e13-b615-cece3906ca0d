import React, { useState } from 'react';
import { StyleSheet, View, TouchableOpacity, Image, ScrollView, } from 'react-native';
import { useRouter } from 'expo-router';
import ThemedText from '../../components/ThemedText';
import ThemedView from '../../components/ThemedView';
import { Colors } from '../../constants/Colors';
import { shadowPresets } from '../../utils/shadowUtils';

const AllChats = () => {
  const router = useRouter();
  const [activeTab, setActiveTab] = useState('评论我的');

  const tabs = ['评论我的', '我评论的'];

  const commentsData = [
    {
      id: 1,
      user: '三三',
      avatar: require('../../assets/Community_image/AvatarOne.png'),
      date: '2025-05-14',
      content: '回复了我的帖子',
      message: '快去小芒九点半抢到的',
      replyTo: {
        user: '灰狼',
        content: '新发布的博德之门卖完了0-0',
        avatar: require('../../assets/Community_image/AvatarTwo.png')
      }
    }
  ];

  return (
    <ThemedView style={styles.container}>
      {/* 安全视图 */}
      <View style={styles.safeArea} />

      {/* 顶部标题栏 */}
      <View style={styles.header}>
        <TouchableOpacity onPress={() => {
          console.log('Back button pressed in AllChats');
          if (router.canGoBack()) {
            console.log('Can go back, using router.back()');
            router.back();
          } else {
            console.log('Cannot go back, navigating to Chat');
            router.push('/Chat');
          }
        }}>
          <Image source={require('../../assets/FrameTwo.png')} style={styles.backIcon} />
        </TouchableOpacity>
        <ThemedText style={styles.headerTitle}>消息</ThemedText>
        <TouchableOpacity>
          <Image source={require('../../assets/FrameFour.png')} style={styles.settingsIcon} />
        </TouchableOpacity>
      </View>

      {/* 标签栏 */}
      <View style={styles.tabContainer}>
        {tabs.map((tab) => (
          <TouchableOpacity
            key={tab}
            style={[styles.tab, activeTab === tab && styles.activeTab]}
            onPress={() => setActiveTab(tab)}
          >
            <ThemedText style={[styles.tabText, activeTab === tab && styles.activeTabText]}>
              {tab}
            </ThemedText>
          </TouchableOpacity>
        ))}
      </View>

      {/* 评论列表 */}
      <ScrollView style={styles.commentsList} showsVerticalScrollIndicator={false}>

        {/* 评论列表 */}
        <View style={styles.commentsList}>
          {commentsData.map((comment) => (
            <View key={comment.id} style={styles.commentItem}>
              <Image source={comment.avatar} style={styles.avatar} />
              <View style={styles.commentContent}>
                <View style={styles.commentHeader}>
                  <ThemedText style={styles.userName}>{comment.user}</ThemedText>
                  <ThemedText style={styles.commentDate}>{comment.date} {comment.content}</ThemedText>
                </View>
                <ThemedText style={styles.commentMessage}>{comment.message}</ThemedText>

                {/* 被回复的内容 */}
                {comment.replyTo && (
                  <View style={styles.replyToContainer}>
                    <Image source={comment.replyTo.avatar} style={styles.replyAvatar} />
                    <View style={styles.replyContent}>
                      <ThemedText style={styles.replyUser}>{comment.replyTo.user}:</ThemedText>
                      <ThemedText style={styles.replyText}>{comment.replyTo.content}</ThemedText>
                    </View>
                  </View>
                )}
              </View>
            </View>
          ))}
        </View>
      </ScrollView>
    </ThemedView>

  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  safeArea: {
    height: 50,
    backgroundColor: '#fff',
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    zIndex: 15,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingTop: 10,
    paddingBottom: 16,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  backIcon: {
    marginTop: 15,
    width: 24,
    height: 24,
    resizeMode: 'contain',
  },
  headerTitle: {
    marginTop: 20,
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.light.title,
  },
  settingsIcon: {
    marginTop: 15,
    width: 24,
    height: 24,
    resizeMode: 'contain',
  },
  tabContainer: {
    flexDirection: 'row',
    backgroundColor: '#f5f5f5',
    borderRadius: 20,
    padding: 2,
    marginHorizontal: 20,
    marginVertical: 16,
  },
  tab: {
    flex: 1,
    paddingHorizontal: 20,
    paddingVertical: 8,
    borderRadius: 18,
    alignItems: 'center',
  },
  activeTab: {
    backgroundColor: '#fff',
    ...shadowPresets.light,
  },
  tabText: {
    fontSize: 14,
    color: '#666',
  },
  activeTabText: {
    color: '#333',
    fontWeight: '500',
  },
  commentsList: {
    flex: 1,
    backgroundColor: '#fff',
  },
  commentItem: {
    flexDirection: 'row',
    padding: 16,
    backgroundColor: '#fff',
    marginBottom: 1,
  },
  avatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
    marginRight: 12,
  },
  commentContent: {
    flex: 1,
  },
  commentHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  userName: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#333',
    marginRight: 8,
  },
  commentDate: {
    fontSize: 12,
    color: '#999',
  },
  commentMessage: {
    fontSize: 14,
    color: '#333',
    lineHeight: 20,
    marginBottom: 8,
  },
  replyToContainer: {
    flexDirection: 'row',
    backgroundColor: '#f5f5f5',
    borderRadius: 8,
    padding: 8,
    marginTop: 4,
  },
  replyAvatar: {
    width: 30,
    height: 30,
    borderRadius: 15,
    marginRight: 8,
  },
  replyContent: {
    flex: 1,
  },
  replyUser: {
    fontSize: 12,
    color: '#666',
    fontWeight: '500',
  },
  replyText: {
    fontSize: 12,
    color: '#666',
    lineHeight: 16,
  },
});

export default AllChats;
