import React, { useEffect } from 'react';
import { StyleSheet, View, Image, Text, TouchableOpacity, ScrollView } from 'react-native';
import ThemedView from '../components/ThemedView';
import ThemedText from '../components/ThemedText';
import { useRouter } from 'expo-router';
import { Colors } from '../constants/Colors';

export default function CommunityStudy() {
  const router = useRouter();

  // 清理函数，确保在组件卸载时清理状态
  useEffect(() => {
    return () => {
      // 清理任何可能的焦点状态
    };
  }, []);

  return (

    <ScrollView style={{ flex: 1, backgroundColor: '#FFF8F3' }} showsVerticalScrollIndicator={false}>
      {/* 顶部标题栏 */}
      <View style={styles.header}>
        <TouchableOpacity
          onPress={() => router.push('/(dashboard)/community?tab=community&filterTag=自习室')}
          accessible={true}
          accessibilityRole="button"
          accessibilityLabel="返回社区页面"
        >
          <Image source={require('../assets/FrameTwo.png')} style={[styles.backIcon]} />
        </TouchableOpacity>
        <ThemedText style={styles.headerTitle}>自习室</ThemedText>
        <TouchableOpacity
          accessible={true}
          accessibilityRole="button"
          accessibilityLabel="更多选项"
        >
          <ThemedText style={styles.headerMenu}>⋯</ThemedText>
        </TouchableOpacity>
      </View>
      {/* 统计区 */}
      <View style={styles.statsRow}>
        <Image source={require('../assets/standdog.png')} style={styles.statsDog} />
        <View style={{ flex: 1 }}>
          <ThemedText style={styles.statsText}>自习时天数：<Text style={styles.statsNum}>91</Text>天</ThemedText>
          <ThemedText style={styles.statsText}>当前连续自习：<Text style={styles.statsNum}>39</Text>天</ThemedText>
          <ThemedText style={styles.statsText}>累计专注时间：<Text style={styles.statsNum}>881h</Text></ThemedText>
        </View>
        <Image source={require('../assets/Plan_image/Sun.png')} style={styles.statsSun} />
      </View>
      {/* 功能按钮区 */}
      <View style={styles.funcRow}>
        <TouchableOpacity
          style={[styles.funcBtnSquare, styles.funcBtnMusic]}
          onPress={() => {
            // TODO: 跳转到音乐陪伴页面
            console.log('点击音乐陪伴');
          }}
          accessible={true}
          accessibilityRole="button"
          accessibilityLabel="音乐陪伴"
        >
          <ThemedText style={styles.funcBtnText}>音乐陪伴</ThemedText>
          <Image source={require('../assets/favicon.png')} style={styles.funcBtnImg} />
        </TouchableOpacity>
        <TouchableOpacity
          style={[styles.funcBtnSquare, styles.funcBtnStudyRoom]}
          onPress={() => {
            router.push('/Community/StudyroomCreate');
          }}
          accessible={true}
          accessibilityRole="button"
          accessibilityLabel="新建自习室"
        >
          <ThemedText style={styles.funcBtnText}>新建自习室</ThemedText>
          <Image source={require('../assets/favicon.png')} style={styles.funcBtnImg} />
        </TouchableOpacity>
        <TouchableOpacity
          style={[styles.funcBtnSquare, styles.funcBtnHomework]}
          onPress={() => {
            // TODO: 跳转到作业无忧页面
            console.log('点击作业无忧');
          }}
          accessible={true}
          accessibilityRole="button"
          accessibilityLabel="作业无忧"
        >
          <ThemedText style={styles.funcBtnText}>作业无忧</ThemedText>
          <Image source={require('../assets/favicon.png')} style={styles.funcBtnImg} />
        </TouchableOpacity>
      </View>
      {/* 自习室列表 */}
      <View style={{ paddingBottom: 20 }}>
        <ThemedText style={{ fontSize: 20, fontWeight: 'bold', color: '#222', marginLeft: 24, marginBottom: 16 }}>自习室</ThemedText>
        {/* 我的自习室卡片 */}
        <View style={styles.studyCard}>
          <View style={{ flexDirection: 'row', alignItems: 'center' }}>
            <View style={{ flex: 1 }}>
              <ThemedText style={styles.studyCardTitle}>我的自习室</ThemedText>
              <ThemedText style={styles.studyCardDesc}>考研  大一</ThemedText>
              <ThemedText style={styles.studyCardTime}>自习时间 | 18:00-23:00</ThemedText>
            </View>
            <Image source={require('../assets/dogreadbook.png')} style={styles.studyCardDog} />
            <View style={{ flexDirection: 'column', justifyContent: 'flex-end', alignItems: 'flex-end', flex: 0, marginTop: 60 }}>
              <TouchableOpacity onPress={() => { /* TODO: 分享功能 */ }}>
                <Image source={require('../assets/Community_image/SelfSrudyShare.png')} style={styles.studyCardShare} />
              </TouchableOpacity>
            </View>
          </View>
        </View>
        {/* 其他自习室卡片 */}
        <View style={styles.studyCard}>
          <View style={{ flexDirection: 'row', alignItems: 'center' }}>
            <View style={{ flex: 1 }}>
              <ThemedText style={styles.studyCardTitle}>Caaary建的自习室</ThemedText>
              <ThemedText style={styles.studyCardDesc}>日常学习  研究生日常</ThemedText>
              <ThemedText style={styles.studyCardTime}>自习时间 | 18:30-21:00</ThemedText>
            </View>
            <Image source={require('../assets/dogreadbook.png')} style={styles.studyCardDog} />
            <View style={{ flexDirection: 'column', justifyContent: 'flex-end', alignItems: 'flex-end', flex: 0, marginTop: 60 }}>
              <TouchableOpacity onPress={() => { /* TODO: 分享功能 */ }}>
                <Image source={require('../assets/Community_image/SelfSrudyShare.png')} style={styles.studyCardShare} />
              </TouchableOpacity>
            </View>
          </View>
        </View>
        <View style={styles.studyCard}>
          <View style={{ flexDirection: 'row', alignItems: 'center' }}>
            <View style={{ flex: 1 }}>
              <ThemedText style={styles.studyCardTitle}>Caaary建的自习室</ThemedText>
              <ThemedText style={styles.studyCardDesc}>日常学习  研究生日常</ThemedText>
              <ThemedText style={styles.studyCardTime}>自习时间 | 18:30-21:00</ThemedText>
            </View>
            <Image source={require('../assets/dogreadbook.png')} style={styles.studyCardDog} />
            <View style={{ flexDirection: 'column', justifyContent: 'flex-end', alignItems: 'flex-end', flex: 0, marginTop: 60 }}>
              <TouchableOpacity onPress={() => { /* TODO: 分享功能 */ }}>
                <Image source={require('../assets/Community_image/SelfSrudyShare.png')} style={styles.studyCardShare} />
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </View>
    </ScrollView>

  );
}

const styles = StyleSheet.create({
  header: {
    flexDirection: 'row', alignItems: 'center', paddingTop: 60, paddingHorizontal: 16, backgroundColor: 'transparent', marginBottom: 24
  },
  backIcon: { width: 28, height: 28, marginRight: 8 },
  headerTitle: { flex: 1, fontSize: 22, fontWeight: 'bold', color: '#222', textAlign: 'center' },
  headerMenu: { fontSize: 24, color: '#bbb' },
  statsRow: { flexDirection: 'row', alignItems: 'center', marginHorizontal: 16, marginBottom: 18 },
  statsDog: { width: 90, height: 90, marginRight: 16 },
  statsText: { fontSize: 15, color: '#222', marginBottom: 2 },
  statsNum: { fontWeight: 'bold', color: '#888' },
  statsSun: { width: 54, height: 54, marginLeft: 12 },
  funcRow: { flexDirection: 'row', justifyContent: 'space-between', marginHorizontal: 16, marginBottom: 18 },
  funcBtnSquare: { flex: 1, backgroundColor: Colors.light.uiBackground, borderRadius: 16, alignItems: 'center', justifyContent: 'space-between', aspectRatio: 1, marginHorizontal: 4, paddingVertical: 12, overflow: 'hidden' },
  funcBtnMusic: { backgroundColor: '#ffcda4ff' },
  funcBtnStudyRoom: { backgroundColor: '#FED8CE' },
  funcBtnHomework: { backgroundColor: '#DCECF9' },
  funcBtnText: { fontSize: 16, color: '#888', marginTop: 8 },
  funcBtnImg: { width: '80%', height: undefined, aspectRatio: 1, marginTop: 'auto', marginBottom: 8, borderRadius: 12 },
  studyCard: { backgroundColor: '#fff', borderRadius: 18, marginHorizontal: 16, marginBottom: 16, padding: 18, shadowColor: '#000', shadowOpacity: 0.04, shadowRadius: 8, minHeight: 140 },
  studyCardTitle: { fontSize: 16, fontWeight: 'bold', color: '#222', marginBottom: 2, marginTop: 0 },
  studyCardDesc: { fontSize: 13, color: '#888', marginBottom: 8 },
  studyCardTime: { fontSize: 13, color: '#aaa', marginBottom: 8, marginTop: 40 },
  studyCardDog: { width: 80, height: 80, borderRadius: 27, marginRight: 60 },
  studyCardArrowWrap: { backgroundColor: '#F2F2F2', borderRadius: 20, padding: 8 },
  studyCardArrow: { width: 24, height: 24 },
  studyCardShare: { width: 36, height: 36, marginTop: 16, marginRight: 0 },
});