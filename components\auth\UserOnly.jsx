import { useUser } from '../../hooks/useUser'
import { useRouter } from 'expo-router'
import { useEffect } from 'react'

import ThemedLoader from '../ThemedLoader'

const UserOnly = ({ children }) => {
  const { user, authChecked } = useUser()
  const router = useRouter()

  useEffect(() => {
    if (authChecked && user === null) {
      console.log('UserOnly: 用户未登录，跳转到登录页面');
      router.replace("/login")
    }
  }, [user, authChecked])

  console.log('UserOnly: 用户状态检查', { user: !!user, authChecked });

  // show loader while we wait for auth to be checked, or while redirecting if user becomes null
  if (!authChecked || !user) {
    console.log('UserOnly: 显示加载器');
    return null; // 简化加载状态，避免闪屏
  }

  console.log('UserOnly: 渲染子组件');
  return children
}

export default UserOnly