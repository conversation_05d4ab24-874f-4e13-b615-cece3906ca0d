import { Platform, Dimensions } from 'react-native';

/**
 * 获取当前设备的屏幕尺寸信息
 * @returns {Object} 包含宽度、高度和平台信息的对象
 */
export const getScreenDimensions = () => {
  const { width, height } = Dimensions.get('window');
  const { width: screenWidth, height: screenHeight } = Dimensions.get('screen');
  
  return {
    window: { width, height },
    screen: { width: screenWidth, height: screenHeight },
    isWeb: Platform.OS === 'web',
    isMobile: Platform.OS === 'ios' || Platform.OS === 'android',
    platform: Platform.OS
  };
};

/**
 * 检查是否为小屏幕设备
 * @param {number} breakpoint - 断点宽度，默认768px
 * @returns {boolean} 是否为小屏幕
 */
export const isSmallScreen = (breakpoint = 768) => {
  const { width } = Dimensions.get('window');
  return width < breakpoint;
};

/**
 * 检查是否为平板设备
 * @returns {boolean} 是否为平板
 */
export const isTablet = () => {
  const { width, height } = Dimensions.get('window');
  const minDimension = Math.min(width, height);
  const maxDimension = Math.max(width, height);
  
  // 平板通常最小尺寸大于600px，且宽高比不会太极端
  return minDimension >= 600 && maxDimension / minDimension < 2;
};

/**
 * 获取响应式的样式值
 * @param {Object} values - 不同屏幕尺寸对应的值
 * @param {*} values.mobile - 移动端值
 * @param {*} values.tablet - 平板值（可选）
 * @param {*} values.desktop - 桌面端值（可选）
 * @returns {*} 对应当前屏幕的值
 */
export const getResponsiveValue = (values) => {
  const { width } = Dimensions.get('window');
  const isWeb = Platform.OS === 'web';
  
  if (isWeb) {
    if (width >= 1024 && values.desktop !== undefined) {
      return values.desktop;
    }
    if (width >= 768 && values.tablet !== undefined) {
      return values.tablet;
    }
  }
  
  if (isTablet() && values.tablet !== undefined) {
    return values.tablet;
  }
  
  return values.mobile;
};

/**
 * 获取Web端安全的容器样式
 * 解决Web端初始加载时尺寸不兼容的问题
 * @param {Object} options - 配置选项
 * @param {number} options.maxWidth - 最大宽度
 * @param {boolean} options.center - 是否居中
 * @param {number} options.minHeight - 最小高度
 * @returns {Object} 样式对象
 */
export const getWebSafeContainerStyle = ({
  maxWidth = 1200,
  center = true,
  minHeight = '100vh'
} = {}) => {
  if (Platform.OS !== 'web') {
    return {};
  }
  
  return {
    maxWidth,
    minHeight,
    width: '100%',
    ...(center && {
      marginLeft: 'auto',
      marginRight: 'auto'
    })
  };
};

/**
 * 获取响应式的内边距
 * @param {Object} padding - 内边距配置
 * @param {number} padding.mobile - 移动端内边距
 * @param {number} padding.tablet - 平板内边距
 * @param {number} padding.desktop - 桌面端内边距
 * @returns {number} 对应的内边距值
 */
export const getResponsivePadding = (padding) => {
  return getResponsiveValue({
    mobile: padding.mobile || 16,
    tablet: padding.tablet || 24,
    desktop: padding.desktop || 32
  });
};

/**
 * 获取响应式的字体大小
 * @param {Object} fontSize - 字体大小配置
 * @param {number} fontSize.mobile - 移动端字体大小
 * @param {number} fontSize.tablet - 平板字体大小
 * @param {number} fontSize.desktop - 桌面端字体大小
 * @returns {number} 对应的字体大小
 */
export const getResponsiveFontSize = (fontSize) => {
  return getResponsiveValue({
    mobile: fontSize.mobile,
    tablet: fontSize.tablet || fontSize.mobile * 1.1,
    desktop: fontSize.desktop || fontSize.mobile * 1.2
  });
};

/**
 * 监听屏幕尺寸变化
 * @param {Function} callback - 回调函数
 * @returns {Function} 取消监听的函数
 */
export const subscribeToOrientationChange = (callback) => {
  const subscription = Dimensions.addEventListener('change', callback);
  
  return () => {
    if (subscription?.remove) {
      subscription.remove();
    }
  };
};
