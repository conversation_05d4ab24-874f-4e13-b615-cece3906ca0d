// components/SafeImage.jsx - 优化版本，避免重复URL处理和闪屏
import React, { useState, useMemo, useCallback } from 'react';
import { Image, View, Platform } from 'react-native';
import { getSafeImageUrl } from '../lib/imageUtils';
import ThemedText from './ThemedText';

const SafeImage = ({
  source,
  style,
  placeholder = 'https://via.placeholder.com/300x200?text=No+Image',
  showDebugInfo = false,
  onError,
  onLoad,
  ...props
}) => {
  const [imageError, setImageError] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  // 使用useMemo缓存图片源处理结果，避免重复计算
  const imageSource = useMemo(() => {
    if (!source) return { uri: placeholder };

    if (typeof source === 'string') {
      const transformedUrl = getSafeImageUrl(source, placeholder);
      return { uri: transformedUrl };
    }

    if (source.uri) {
      const transformedUrl = getSafeImageUrl(source.uri, placeholder);
      return { ...source, uri: transformedUrl };
    }

    // 如果是本地图片资源，直接返回
    return source;
  }, [source, placeholder]);

  // 使用useCallback优化事件处理函数，避免重复创建
  const handleError = useCallback((error) => {
    console.error(`[${Platform.OS}] 图片加载失败:`, {
      originalSource: source,
      transformedSource: imageSource,
      error: error.nativeEvent?.error
    });
    setImageError(true);
    setIsLoading(false);
    if (onError) onError(error);
  }, [source, imageSource, onError]);

  const handleLoad = useCallback((event) => {
    setIsLoading(false);
    setImageError(false);
    if (onLoad) onLoad(event);

    if (showDebugInfo) {
      console.log(`[${Platform.OS}] 图片加载成功:`, {
        originalSource: source,
        transformedSource: imageSource
      });
    }
  }, [onLoad, showDebugInfo, source, imageSource]);

  const handleLoadStart = useCallback(() => {
    setIsLoading(true);
    setImageError(false);
  }, []);

  // 如果图片加载失败，显示占位符
  if (imageError) {
    return (
      <View style={[style, { 
        backgroundColor: '#f0f0f0', 
        justifyContent: 'center', 
        alignItems: 'center',
        borderWidth: 1,
        borderColor: '#ddd',
        borderStyle: 'dashed'
      }]}>
        <ThemedText style={{ 
          fontSize: 12, 
          color: '#999', 
          textAlign: 'center',
          padding: 10
        }}>
          图片加载失败
        </ThemedText>
        {showDebugInfo && (
          <ThemedText style={{ 
            fontSize: 10, 
            color: '#666', 
            textAlign: 'center',
            marginTop: 5
          }}>
            {typeof source === 'string' ? source : source?.uri}
          </ThemedText>
        )}
      </View>
    );
  }

  return (
    <View style={style}>
      <Image
        {...props}
        source={imageSource}
        style={[
          style,
          // 优化加载状态，避免闪屏
          isLoading && {
            opacity: 0.8,  // 减少透明度变化，避免明显闪烁
            backgroundColor: '#f5f5f5'  // 添加背景色，减少白屏效果
          }
        ]}
        onError={handleError}
        onLoad={handleLoad}
        onLoadStart={handleLoadStart}
        // 添加缓存策略，减少重复加载
        cache="force-cache"
        // 预加载策略
        loadingIndicatorSource={{ uri: placeholder }}
      />
      {showDebugInfo && isLoading && (
        <View style={{
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          backgroundColor: 'rgba(0,0,0,0.3)',  // 减少遮罩透明度
          justifyContent: 'center',
          alignItems: 'center'
        }}>
          <ThemedText style={{ color: 'white', fontSize: 12 }}>
            加载中...
          </ThemedText>
        </View>
      )}
    </View>
  );
};

export default SafeImage;
