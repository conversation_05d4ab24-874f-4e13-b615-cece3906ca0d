import React, { useEffect, useRef, useState } from 'react';
import { StyleSheet, View, ScrollView, TouchableOpacity, Image, Text, Animated, Dimensions, Alert, Easing } from 'react-native';
import ThemedView from '../components/ThemedView';
import ThemedText from '../components/ThemedText';
import { useRouter } from 'expo-router';
import Svg, { Path, Circle, Rect } from 'react-native-svg';

const { width, height } = Dimensions.get('window');

// 生成60%-95%之间的随机匹配度
const generateRandomPercentage = () => {
  return Math.floor(Math.random() * (95 - 60 + 1)) + 60;
};

// SVG卡片组件 - 橘色卡片
const Group336 = ({ style, percentage }) => (
  <Animated.View style={style}>
    <View style={tagStyles.orangeCardContainer}>
      {/* SVG背景 */}
      <Svg width="177" height="228" viewBox="0 0 177 228" fill="none" style={tagStyles.svgBackground}>
        <Path
          d="M101.303 0.887248C120.703 -3.4686 139.96 8.72629 144.316 28.1255L175.644 167.651C179.906 186.632 168.323 205.474 149.65 210.358L112.988 207.236C111.036 207.07 109.084 207.545 107.426 208.589L79.6206 226.108L75.2281 227.095C55.829 231.451 36.5714 219.256 32.2154 199.857L0.887248 60.3306C-3.4686 40.9314 8.72629 21.6738 28.1255 17.3179L101.303 0.887248Z"
          fill="#FFDDB4"
        />
        <Circle
          cx="39.7023"
          cy="55.7582"
          r="27"
          transform="rotate(2.58712 39.7023 55.7582)"
          fill="#FFC680"
          stroke="#7A3C10"
          strokeDasharray="6 6"
        />
        <Circle
          cx="110.864"
          cy="30.8843"
          r="20"
          transform="rotate(2.58712 110.864 30.8843)"
          fill="#FFDDB4"
        />
        <Circle
          cx="110.864"
          cy="30.8843"
          r="19.5"
          transform="rotate(2.58712 110.864 30.8843)"
          stroke="#B06509"
          strokeOpacity="0.35"
        />
        <Rect
          x="20.2693"
          y="100.821"
          width="79"
          height="25"
          rx="12.5"
          transform="rotate(-13.6329 20.2693 100.821)"
          fill="#FFDDB4"
          stroke="#B06509"
        />
        <Path
          d="M99.8663 198.713C85.5264 205.919 80.4751 217.703 67.5333 216.117C55.0009 216.552 47.8694 208.222 45.7332 200.117C43.4358 191.401 50.4907 161.292 86.851 176.603C116.815 189.22 120.34 158.705 136.854 155.184C171.725 147.751 172.419 194.77 147.923 198.227C131.804 200.501 116.412 190.398 99.8663 198.713Z"
          fill="#FFC680"
          stroke="#FFC680"
          strokeWidth="7"
          strokeLinecap="round"
        />
        <Circle
          cx="143.801"
          cy="177.02"
          r="25.5"
          transform="rotate(2.58712 143.801 177.02)"
          fill="#FFC680"
        />
      </Svg>

      {/* 左上角匹配度 */}
      <View style={tagStyles.orangeMatchPercentageOverlay}>
        <Text style={tagStyles.orangePercentageNumber}>{percentage}%</Text>
        <Text style={tagStyles.orangePercentageLabel}>匹配度</Text>
      </View>

      {/* 右上角图标 */}
      <View style={tagStyles.orangeTopRightIcon}>
        <Image
          source={require('../assets/Community_image/Creat.png')}
          style={tagStyles.orangeCreatIcon}
        />
      </View>

      {/* 头像 */}
      <View style={tagStyles.orangeAvatarsContainer}>
        <Image
          source={require('../assets/Community_image/AvatarFour.png')}
          style={tagStyles.orangeAvatar}
        />
      </View>
    </View>
  </Animated.View>
);

const Group337 = ({ style, percentage }) => (
  <Animated.View style={style}>
    <View style={tagStyles.blueCardContainer}>
      {/* SVG背景 */}
      <Svg width="190" height="247" viewBox="0 0 190 247" fill="none" style={tagStyles.svgBackground}>
        <Path
          d="M48.3454 24.3315C54.7952 5.52464 75.2699 -4.49303 94.0768 1.95655L165.021 26.2866C183.828 32.7364 193.846 53.2112 187.396 72.0181L141.007 207.285C134.557 226.092 114.082 236.109 95.2751 229.66L93.1432 228.928L64.1149 207.485C62.5395 206.321 60.6272 205.704 58.6686 205.725L26.5124 206.078L24.3307 205.33C5.52393 198.88 -4.49305 178.405 1.95672 159.598L48.3454 24.3315Z"
          fill="#DCECF9"
        />
        <Circle
          cx="83.8059"
          cy="40.7663"
          r="27"
          transform="rotate(34.1715 83.8059 40.7663)"
          fill="#93B6D2"
          stroke="#436F93"
          strokeDasharray="6 6"
        />
        <Circle
          cx="157.454"
          cy="56.848"
          r="20"
          transform="rotate(34.1715 157.454 56.848)"
          fill="#DCECF9"
        />
        <Circle
          cx="157.454"
          cy="56.848"
          r="19.5"
          transform="rotate(34.1715 157.454 56.848)"
          stroke="#436F93"
          strokeOpacity="0.35"
        />
        <Rect
          x="44.4875"
          y="67.843"
          width="79"
          height="25"
          rx="12.5"
          transform="rotate(19.9004 44.4875 67.843)"
          fill="#DCECF9"
          stroke="#436F93"
        />
        <Path
          d="M60.5285 192.446C44.5281 191.201 34.1118 198.676 23.8539 190.627C12.9018 184.52 11.1044 173.702 13.4662 165.66C16.0062 157.011 37.6119 134.887 60.8192 166.792C79.9442 193.084 98.7387 168.786 114.694 174.311C148.386 185.977 124.669 226.583 101.915 216.877C86.9412 210.49 78.9902 193.883 60.5285 192.446Z"
          fill="#93B6D2"
          stroke="#93B6D2"
          strokeWidth="7"
          strokeLinecap="round"
        />
        <Circle
          cx="109.351"
          cy="196.593"
          r="25"
          transform="rotate(33.72 109.351 196.593)"
          fill="#93B6D2"
          stroke="#93B6D2"
        />
      </Svg>

      {/* 左上角匹配度 */}
      <View style={tagStyles.blueMatchPercentageOverlay}>
        <Text style={tagStyles.bluePercentageNumber}>{percentage}%</Text>
        <Text style={tagStyles.bluePercentageLabel}>匹配度</Text>
      </View>

      {/* 右上角图标 */}
      <View style={tagStyles.blueTopRightIcon}>
        <Image
          source={require('../assets/Community_image/Creat.png')}
          style={tagStyles.blueCreatIcon}
        />
      </View>

      {/* 头像 */}
      <View style={tagStyles.blueAvatarsContainer}>
        <Image
          source={require('../assets/Community_image/AvatarSeven.png')}
          style={tagStyles.blueAvatar}
        />
      </View>
    </View>
  </Animated.View>
);

// 合并后的绿色卡片
const MergedCard = ({ style, selectedTags, percentage }) => {
  const router = useRouter();

  return (
    <Animated.View style={style}>
      <View style={tagStyles.mergedCardContainer}>
        {/* SVG背景 */}
        <Svg width="194" height="298" viewBox="0 0 194 298" fill="none" style={tagStyles.svgBackground}>
          <Path
            d="M157.196 0.135765C177.079 0.158122 193.179 16.2945 193.156 36.1768L192.92 246.338C192.897 266.22 176.762 282.319 156.88 282.297L139.261 282.276L94.9834 268.181C93.117 267.586 91.108 267.617 89.2608 268.269L49.8155 282.176L35.96 282.161C16.0778 282.139 -0.0221689 266.003 2.29139e-05 246.121L0.236351 35.96C0.258708 16.0777 16.3951 -0.0223337 36.2774 2.3256e-05L157.196 0.135765Z"
            fill="#D8F0D0"
          />
          <Circle
            cx="50.5477"
            cy="51.8725"
            r="35.5904"
            transform="rotate(15.3783 50.5477 51.8725)"
            fill="#A7CD9C"
            stroke="#74945A"
            strokeDasharray="6 6"
          />
          <Circle
            cx="148.848"
            cy="40.7152"
            r="26.2476"
            transform="rotate(15.3783 148.848 40.7152)"
            fill="#D8F0D0"
          />
          <Circle
            cx="148.848"
            cy="40.7152"
            r="25.7476"
            transform="rotate(15.3783 148.848 40.7152)"
            stroke="#5A9343"
            strokeOpacity="0.35"
          />
          <Rect
            x="12.6631"
            y="102.979"
            width="103.99"
            height="33.1218"
            rx="16.5609"
            fill="#D8F0D0"
            stroke="#74945A"
          />
          <Path
            d="M85.7556 250.163C65.3498 255.38 55.5689 269.071 39.4214 263.408C23.2322 260.451 16.4256 247.771 15.96 236.781C15.4592 224.962 32.9483 188.339 75.2703 218.166C110.147 242.747 123.225 204.612 145.385 204.731C192.176 204.98 179.878 265.457 147.503 263.018C126.2 261.413 109.3 244.143 85.7556 250.163Z"
            fill="#A7CD9C"
            stroke="#A7CD9C"
            strokeWidth="7"
            strokeLinecap="round"
          />
          <Circle
            cx="148.167"
            cy="233.673"
            r="32.9657"
            transform="rotate(14.9268 148.167 233.673)"
            fill="#A7CD9C"
            stroke="#A7CD9C"
          />
        </Svg>

        {/* 左上角匹配度 */}
        <View style={tagStyles.matchPercentageOverlay}>
          <Text style={tagStyles.percentageNumber}>{percentage}%</Text>
          <Text style={tagStyles.percentageLabel}>匹配度</Text>
        </View>

        {/* 右上角图标 */}
        <View style={tagStyles.topRightIcon}>
          <Image
            source={require('../assets/Community_image/Creat.png')}
            style={tagStyles.creatIcon}
          />
        </View>

        {/* 中间用户名 */}
        <View style={tagStyles.userNamesContainer}>
          <Text style={tagStyles.userNames}>Jessica & Lucy</Text>
        </View>

        {/* 底部头像 */}
        <View style={tagStyles.avatarsContainer}>
          <Image
            source={require('../assets/Community_image/AvatarOne.png')}
            style={[tagStyles.avatar, tagStyles.leftAvatar]}
          />
          <Image
            source={require('../assets/Community_image/AvatarTwo.png')}
            style={[tagStyles.avatar, tagStyles.rightAvatar]}
          />
        </View>

        {/* 进入按钮 */}
        <TouchableOpacity
          style={tagStyles.enterButton}
          onPress={() => {
            console.log('按钮被点击，传递标签:', selectedTags);
            const tagsParam = selectedTags.length > 0 ? selectedTags.join(',') : '考试冲刺,拖延症互助,自律型';
            router.push(`/Community/StudyroomMatch?tags=${encodeURIComponent(tagsParam)}`);
          }}
          activeOpacity={0.8}
        >
          <Text style={tagStyles.enterButtonText}>进入</Text>
        </TouchableOpacity>
      </View>
    </Animated.View>
  );
};

// 标签选择组件
const TagSelectionComponent = ({ selectedTags, setSelectedTags, orangePercentage, bluePercentage, greenPercentage }) => {
  const [isMatching, setIsMatching] = useState(false);
  const [isMerged, setIsMerged] = useState(false);
  const [currentMatch, setCurrentMatch] = useState({
    name: 'Jessica',
    percentage: 67,
    isMatching: true
  });

  // 动画值
  const slideOutAnim = useRef(new Animated.Value(0)).current;
  const slideUpAnim = useRef(new Animated.Value(height)).current;
  const pulseAnim = useRef(new Animated.Value(0)).current;
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const card1TranslateX = useRef(new Animated.Value(-80)).current; // 左卡片初始位置
  const card2TranslateX = useRef(new Animated.Value(80)).current;  // 右卡片初始位置
  const cardsOpacity = useRef(new Animated.Value(1)).current;
  const mergedCardScale = useRef(new Animated.Value(0)).current;

  const allTags = [
    '考试冲刺', '夜猫子', '兴趣拓展',
    '自律型', '社恐友好', '拖延症互助',
    '考研'
  ];

  // 四种背景颜色
  const tagColors = ['#F1E2FF', '#FFDDB4', '#E2ECE1', '#DCECF9'];

  // 为每个标签分配颜色（打乱顺序）
  const getTagColor = (index) => {
    return tagColors[index % tagColors.length];
  };

  const toggleTag = (tag) => {
    if (selectedTags.includes(tag)) {
      setSelectedTags(selectedTags.filter(item => item !== tag));
    } else {
      if (selectedTags.length >= 5) {
        Alert.alert('提示', '最多只能选择5个标签');
        return;
      }
      setSelectedTags([...selectedTags, tag]);
    }
  };

  // 匹配动画效果
  useEffect(() => {
    if (isMatching) {
      // 脉冲动画
      Animated.loop(
        Animated.sequence([
          Animated.timing(pulseAnim, {
            toValue: 1,
            duration: 1000,
            easing: Easing.inOut(Easing.ease),
            useNativeDriver: true
          }),
          Animated.timing(pulseAnim, {
            toValue: 0,
            duration: 1000,
            easing: Easing.inOut(Easing.ease),
            useNativeDriver: true
          })
        ])
      ).start();

      // 淡入动画
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 500,
        useNativeDriver: true
      }).start();

      // 3秒后开始合并动画
      const mergeTimer = setTimeout(() => {
        setIsMerged(true);

        // 两张卡片向中心移动并淡出
        Animated.parallel([
          Animated.timing(card1TranslateX, {
            toValue: 0, // 从-80移动到0
            duration: 800,
            easing: Easing.inOut(Easing.ease),
            useNativeDriver: true
          }),
          Animated.timing(card2TranslateX, {
            toValue: 0, // 从80移动到0
            duration: 800,
            easing: Easing.inOut(Easing.ease),
            useNativeDriver: true
          }),
          Animated.timing(cardsOpacity, {
            toValue: 0,
            duration: 800,
            useNativeDriver: true
          })
        ]).start(() => {
          // 合并卡片出现动画
          Animated.spring(mergedCardScale, {
            toValue: 1,
            tension: 100,
            friction: 8,
            useNativeDriver: true
          }).start();
        });
      }, 3000);

      return () => clearTimeout(mergeTimer);
    }
  }, [isMatching]);

  const handleMatch = () => {
    console.log('点击了匹配按钮，当前选择的标签数量:', selectedTags.length);
    console.log('选择的标签:', selectedTags);

    if (selectedTags.length < 3) {
      Alert.alert('提示', '请至少选择3个标签');
      return;
    }

    console.log('开始匹配动画');
    // 开始匹配动画
    setIsMatching(true);

    // 标签内容向右滑出
    Animated.timing(slideOutAnim, {
      toValue: width,
      duration: 500,
      easing: Easing.inOut(Easing.ease),
      useNativeDriver: true
    }).start();

    // 匹配界面从底部滑入
    setTimeout(() => {
      Animated.timing(slideUpAnim, {
        toValue: 0,
        duration: 600,
        easing: Easing.out(Easing.ease),
        useNativeDriver: true
      }).start();
    }, 300);
  };

  // 脉冲效果插值
  const pulseScale = pulseAnim.interpolate({
    inputRange: [0, 1],
    outputRange: [1, 1.05]
  });

  return (
    <View style={tagStyles.container}>
      {/* 标签选择界面 */}
      <Animated.View style={[
        tagStyles.tagSelectionContainer,
        {
          transform: [{ translateX: slideOutAnim }]
        }
      ]}>
        <Text style={tagStyles.title}>可选择3-5个标签</Text>

        <View style={tagStyles.tagsContainer}>
          {allTags.map((tag, index) => (
            <TouchableOpacity
              key={index}
              style={[
                tagStyles.tag,
                { backgroundColor: getTagColor(index) },
                selectedTags.includes(tag) && tagStyles.selectedTag
              ]}
              onPress={() => toggleTag(tag)}
            >
              <Text style={tagStyles.tagText}>
                {tag}
              </Text>
              {selectedTags.includes(tag) && (
                <View style={tagStyles.checkmarkContainer}>
                  <View style={tagStyles.checkmark}>
                    <Text style={tagStyles.checkmarkText}>✓</Text>
                  </View>
                </View>
              )}
            </TouchableOpacity>
          ))}
        </View>

        <TouchableOpacity
          style={tagStyles.matchButton}
          onPress={handleMatch}
          activeOpacity={0.7}
          accessible={true}
          accessibilityRole="button"
          accessibilityLabel="开始匹配"
        >
          <Text style={tagStyles.matchButtonText}>直接开始匹配</Text>
        </TouchableOpacity>

        <Text style={tagStyles.selectedCount}>
          已选择: {selectedTags.length}/5
        </Text>
      </Animated.View>

      {/* 匹配界面 */}
      {isMatching && (
        <Animated.View style={[
          tagStyles.matchingContainer,
          {
            transform: [{ translateY: slideUpAnim }]
          }
        ]}>
          {/* 匹配标题 */}
          <Animated.View style={[tagStyles.titleContainer, { opacity: fadeAnim }]}>
            <ThemedText style={tagStyles.matchingTitle}>
              {isMerged ? '匹配成功！' : '正在为您匹配学习伙伴'}
            </ThemedText>
            <ThemedText style={tagStyles.matchingSubtitle}>
              {isMerged ? '找到了合适的学习伙伴' : '请稍候...'}
            </ThemedText>
          </Animated.View>

          {/* SVG卡片动画区域 */}
          <View style={tagStyles.svgContainer}>
            {!isMerged ? (
              <>
                {/* 匹配中的两张卡片 */}
                <Group336
                  style={[
                    tagStyles.svgCard,
                    {
                      transform: [
                        { translateX: card1TranslateX },
                        {
                          scale: pulseAnim.interpolate({
                            inputRange: [0, 1],
                            outputRange: [0.8, 1.0]
                          })
                        },
                        {
                          rotate: pulseAnim.interpolate({
                            inputRange: [0, 1],
                            outputRange: ['-3deg', '3deg']
                          })
                        }
                      ],
                      opacity: Animated.multiply(fadeAnim, cardsOpacity)
                    }
                  ]}
                  percentage={orangePercentage}
                />
                <Group337
                  style={[
                    tagStyles.svgCard,
                    {
                      transform: [
                        { translateX: card2TranslateX },
                        {
                          scale: pulseAnim.interpolate({
                            inputRange: [0, 1],
                            outputRange: [1.0, 0.8]
                          })
                        },
                        {
                          rotate: pulseAnim.interpolate({
                            inputRange: [0, 1],
                            outputRange: ['3deg', '-3deg']
                          })
                        }
                      ],
                      opacity: Animated.multiply(fadeAnim, cardsOpacity)
                    }
                  ]}
                  percentage={bluePercentage}
                />
              </>
            ) : (
              /* 合并后的绿色卡片 */
              <MergedCard
                style={[
                  tagStyles.mergedCard,
                  {
                    transform: [{ scale: mergedCardScale }],
                    opacity: fadeAnim
                  }
                ]}
                selectedTags={selectedTags}
                percentage={greenPercentage}
              />
            )}
          </View>

          {/* 底部动画点 */}
          {!isMerged && (
            <View style={tagStyles.dotsContainer}>
              {[0, 1, 2].map((i) => (
                <Animated.View
                  key={i}
                  style={[
                    tagStyles.dot,
                    {
                      opacity: pulseAnim.interpolate({
                        inputRange: [0, 0.5, 1],
                        outputRange: [0.3, 1, 0.3]
                      }),
                      transform: [{
                        scale: pulseAnim.interpolate({
                          inputRange: [0, 1],
                          outputRange: [1, 1.2]
                        })
                      }]
                    }
                  ]}
                />
              ))}
            </View>
          )}
        </Animated.View>
      )}
    </View>
  );
};

const flavors = [
  {
    name: ["Chai", "Vanilla"],
    color: "#4A90E2",
    image: require('../assets/qweqw.png'),
    nutrition: ["20g", "13g", "15", "1.8g", "1B"],
    description: ["内向沉稳", "理性思考", "细心谨慎", "温和友善"]
  },
  {
    name: ["Maple", "Peanut"],
    color: "#E94B4B",
    image: require('../assets/Community_image/AvatarSecond.png'),
    nutrition: ["35g", "10g", "10", "1.5g", "2B"],
    description: ["外向开朗", "积极主动", "乐观向上", "热情洋溢"]
  },
  {
    name: ["Cacao", "Coconut"],
    color: "#F4D03F",
    image: require('../assets/Community_image/AvatarThird.png'),
    nutrition: ["40g", "25g", "22", "2.2g", "1B"],
    description: ["创意无限", "独立自主", "好奇心强", "富有想象"]
  },
  {
    name: ["Berry", "Blend"],
    color: "#8E44AD",
    image: require('../assets/Community_image/AvatarFirst.png'),
    nutrition: ["28g", "18g", "25", "2.0g", "3B"],
    description: ["善于合作", "责任心强", "耐心细致", "值得信赖"]
  }
];

function StudyCard({ topTip, title, desc }) {
  return (
    <View style={studyCardStyles.cardWrap}>
      <View style={studyCardStyles.card}>
        <Text style={studyCardStyles.topTip}>{topTip}</Text>
        <Text style={studyCardStyles.title}>{title}</Text>
        <Text style={studyCardStyles.desc}>{desc}</Text>
      </View>
      {/* 三角形缺口 */}
      <View style={studyCardStyles.triangle} />
    </View>
  );
}

export default function CommunityTeam() {
  const router = useRouter();
  const [index, setIndex] = useState(0);
  const fadeAnim = useRef(new Animated.Value(1)).current;
  const bgColor = useRef(new Animated.Value(0)).current;
  const [prevColor, setPrevColor] = useState(flavors[0].color);
  const [selectedTags, setSelectedTags] = useState([]);

  // 随机匹配度状态
  const [greenPercentage, setGreenPercentage] = useState(generateRandomPercentage());
  const [orangePercentage, setOrangePercentage] = useState(generateRandomPercentage());
  const [bluePercentage, setBluePercentage] = useState(generateRandomPercentage());

  // 文字动画状态
  const textAnimValues = useRef(
    flavors[0].description.map(() => new Animated.Value(0))
  ).current;

  const transition = () => {
    const nextIndex = (index + 1) % flavors.length;
    changeSlide(nextIndex);
  };

  useEffect(() => {
    const interval = setInterval(transition, 5000);
    return () => clearInterval(interval);
  }, [index]);

  // 初始化文字动画
  useEffect(() => {
    const textAnimations = textAnimValues.map((anim, i) =>
      Animated.timing(anim, {
        toValue: 1,
        duration: 400,
        delay: i * 200,
        useNativeDriver: true
      })
    );

    Animated.stagger(0, textAnimations).start();
  }, []);

  // 清理函数，确保在组件卸载时清理状态
  useEffect(() => {
    return () => {
      // 清理动画状态
      fadeAnim.setValue(1);
    };
  }, []);

  const changeSlide = (newIndex) => {
    // 重置文字动画
    textAnimValues.forEach(anim => anim.setValue(0));

    Animated.timing(fadeAnim, {
      toValue: 0,
      duration: 300,
      useNativeDriver: true
    }).start(() => {
      setPrevColor(flavors[index].color);
      setIndex(newIndex);
      fadeAnim.setValue(0);

      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 600,
        useNativeDriver: true
      }).start(() => {
        // 卡片切换完成后，开始文字动画
        const textAnimations = textAnimValues.map((anim, i) =>
          Animated.timing(anim, {
            toValue: 1,
            duration: 400,
            delay: i * 200, // 每个文字延迟200ms出现
            useNativeDriver: true
          })
        );

        Animated.stagger(0, textAnimations).start();
      });
    });
  };

  const current = flavors[index];

  return (
    <ThemedView style={{ flex: 1, backgroundColor: '#FFF8F3' }}>
      {/* 安全视图 */}
      <View style={styles.safeArea} />

      <ScrollView style={{ flex: 1 }} contentContainerStyle={{ paddingTop: 60, paddingBottom: 32 }}>
        {/* 顶部栏 */}
        <View style={styles.header}>
          <TouchableOpacity
            onPress={() => router.push('/(dashboard)/community?tab=community&filterTag=学习搭子')}
            accessible={true}
            accessibilityRole="button"
            accessibilityLabel="返回社区页面"
          >
            <Image source={require('../assets/FrameTwo.png')} style={[styles.backIcon]} />
          </TouchableOpacity>
          <View style={{ flex: 1 }} />
          <TouchableOpacity
            accessible={true}
            accessibilityRole="button"
            accessibilityLabel="分享"
          >
            <Image source={require('../assets/Share.png')} style={styles.shareIcon} />
          </TouchableOpacity>
          <TouchableOpacity
            accessible={true}
            accessibilityRole="button"
            accessibilityLabel="更多选项"
          >
            <ThemedText style={styles.headerMenu}>⋯</ThemedText>
          </TouchableOpacity>
        </View>
        {/* 头像与昵称 */}
        <View style={styles.profileRow}>
          <Image source={require('../assets/Community_image/HandsomeAvatar.png')} style={styles.avatar} />
          <View style={{ marginLeft: 12 }}>
            <ThemedText style={styles.nickname}>你好,DOG</ThemedText>
            <ThemedText style={styles.level}>Lv.30</ThemedText>
          </View>
        </View>
        {/* 今日学习任务推荐 */}

        {/* 等待配对 */}
        <View style={styles.sectionRow}>
          <ThemedText style={styles.sectionTitle} title>10位用户等待配对</ThemedText>
          <TouchableOpacity>
            <Image source={require('../assets/Arrows_right.png')} style={styles.sectionMoreIcon} />
          </TouchableOpacity>
        </View>
        <View style={styles.avatarRow}>
          {[
            require('../assets/Community_image/AvatarOne.png'),
            require('../assets/Community_image/AcatarThree.png'),
            require('../assets/Community_image/AvatarEight.png'),
            require('../assets/Community_image/AvatarFirst.png'),
            require('../assets/Community_image/AvatarFive.png'),
            require('../assets/Community_image/AvatarFour.png'),
            require('../assets/Community_image/AvatarSeven.png'),
          ].map((img, i) => (
            <Image key={i} source={img} style={styles.waitAvatar} />
          ))}
        </View>
        {/* 标签选择功能 */}
        <TagSelectionComponent
          selectedTags={selectedTags}
          setSelectedTags={setSelectedTags}
          orangePercentage={orangePercentage}
          bluePercentage={bluePercentage}
          greenPercentage={greenPercentage}
        />
        {/* PokerStack组件替换大卡片 */}

        <View style={styles.pokerStackContainer}>
          <ThemedText style={styles.sectionTitlee} title>今日学习搭子</ThemedText>
          <View style={styles.cardWithTextContainer}>
            {/* 左侧卡片 */}
            <Animated.View style={[styles.pokerStackContent, { backgroundColor: current.color }]}>
              <Animated.View style={[styles.pokerStackInner, { opacity: fadeAnim }]}>
                <View style={styles.nameContainer}>
                  <Text style={styles.word}>{current.name[0]}</Text>
                  <Text style={styles.word}>{current.name[1]}</Text>
                </View>

                <Image
                  source={current.image}
                  style={[
                    styles.image,
                    current.name[0] === "Berry" && { width: 140, height: 140 }
                  ]}
                />

                <View style={styles.controls}>
                  {flavors.map((_, i) => (
                    <TouchableOpacity
                      key={i}
                      onPress={() => changeSlide(i)}
                      style={[
                        styles.dot,
                        i === index && styles.activeDot
                      ]}
                    />
                  ))}
                </View>
              </Animated.View>
            </Animated.View>

            {/* 右侧文字区域 */}
            <View style={styles.textContainer}>
              {current.description.map((text, i) => (
                <Animated.View
                  key={i}
                  style={[
                    styles.textItem,
                    {
                      borderLeftColor: current.color,
                      opacity: textAnimValues[i],
                      transform: [{
                        translateY: textAnimValues[i].interpolate({
                          inputRange: [0, 1],
                          outputRange: [20, 0]
                        })
                      }]
                    }
                  ]}
                >
                  <Text style={styles.descriptionText}>{text}</Text>
                </Animated.View>
              ))}
            </View>
          </View>
        </View>
      </ScrollView>
    </ThemedView>

  );
}

const styles = StyleSheet.create({
  safeArea: {
    height: 50,
    backgroundColor: '#FFF8F3',
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    zIndex: 15,
  },
  header: { flexDirection: 'row', alignItems: 'center', paddingTop: 10, paddingHorizontal: 16, backgroundColor: 'transparent', marginBottom: 8 },
  backIcon: { width: 28, height: 28, marginRight: 8 },
  shareIcon: { width: 24, height: 24, marginRight: 8 },
  headerMenu: { fontSize: 24, color: '#bbb' },
  profileRow: { flexDirection: 'row', alignItems: 'center', marginLeft: 16, marginBottom: 8 },
  avatar: { width: 80, height: 80, borderRadius: 40 },
  nickname: { fontSize: 18, fontWeight: 'bold', color: '#222' },
  level: { fontSize: 14, color: '#888', marginTop: 2 },
  sectionRow: { flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between', marginHorizontal: 16, marginTop: 18, marginBottom: 8 },
  sectionTitle: { fontSize: 16, fontWeight: 'bold', color: '#222', paddingBottom: 4 },
  sectionTitlee: { fontSize: 16, fontWeight: 'bold', color: '#222', paddingBottom: 16 },
  sectionMore: { fontSize: 14, color: '#888' },
  taskCard: { backgroundColor: '#F2F2F2', borderRadius: 16, marginHorizontal: 16, marginBottom: 8, padding: 12, alignItems: 'center' },
  taskText: { fontSize: 15, color: '#222' },
  avatarRow: { flexDirection: 'row', alignItems: 'center', marginLeft: 16, marginBottom: 16 },
  waitAvatar: { width: 40, height: 40, borderRadius: 20, backgroundColor: '#E0E0E0', marginRight: 12 },
  cardRow: { flexDirection: 'row', justifyContent: 'space-between', marginHorizontal: 16, marginBottom: 16 },
  studyCard: { flex: 1, borderRadius: 22, marginHorizontal: 4, padding: 16, alignItems: 'flex-start', justifyContent: 'flex-start', minHeight: 160, position: 'relative', height: 50 },
  studyCardIconWrap: { flexDirection: 'row', alignItems: 'center', marginBottom: 8, width: '100%', justifyContent: 'space-between' },
  studyCardIcon: { fontSize: 28, fontWeight: 'bold', color: '#222', backgroundColor: '#fff', borderRadius: 16, paddingHorizontal: 10, paddingVertical: 2 },
  studyCardOutIconWrap: { backgroundColor: '#fff', borderRadius: 16, padding: 4 },
  studyCardOutIcon: { width: 20, height: 20 },
  studyCardLabel: { fontSize: 16, fontWeight: 'bold', color: '#222', marginBottom: 2 },
  studyCardDesc: { fontSize: 13, color: '#222', marginTop: 8, fontWeight: 'bold', transform: [{ rotate: '-10deg' }] },
  pokerStackContainer: { marginLeft: 20, marginRight: 8, marginBottom: 16, height: 280 },
  cardWithTextContainer: { flexDirection: 'row', alignItems: 'center', height: '100%' },
  pokerStackContent: { width: '55%', height: '100%', borderRadius: 20, alignItems: 'center', justifyContent: 'center' },
  pokerStackInner: { width: 280, height: 280, borderRadius: 20, backgroundColor: '#fff0', padding: 15, alignItems: 'center', justifyContent: 'space-between' },
  nameContainer: { flexDirection: 'row', gap: 20, marginTop: 12 },
  word: { fontSize: 24, fontWeight: 'bold', color: '#fff' },
  image: { width: 140, height: 140, resizeMode: 'contain', marginVertical: 8 },

  controls: { flexDirection: 'row', gap: 6, marginBottom: 8 },
  dot: { width: 8, height: 8, borderRadius: 4, backgroundColor: 'rgba(255,255,255,0.4)' },
  activeDot: { backgroundColor: '#fff', transform: [{ scale: 1.3 }] },
  // 文字区域样式
  textContainer: {
    width: '45%',
    height: '100%',
    paddingLeft: 20,
    justifyContent: 'center',
    alignItems: 'flex-start',
  },
  textItem: {
    marginBottom: 16,
    paddingVertical: 12,
    paddingHorizontal: 16,
    backgroundColor: 'rgba(255, 255, 255, 0.15)',
    borderRadius: 12,
    borderLeftWidth: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  descriptionText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#2c3e50',
    textAlign: 'left',
    lineHeight: 22,
  },
  sectionMoreIcon: { width: 20, height: 20, resizeMode: 'contain' },
});

const studyCardStyles = StyleSheet.create({
  cardWrap: {
    flex: 1,
    alignItems: 'center',
    marginHorizontal: 4,
  },
  card: {
    width: '100%',
    backgroundColor: '#F9F199',
    borderRadius: 28,
    paddingTop: 18,
    paddingBottom: 32,
    paddingHorizontal: 18,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.12,
    shadowRadius: 8,
    elevation: 4,
    position: 'relative',
    zIndex: 2,
  },
  topTip: {
    fontSize: 13,
    color: '#aaa',
    marginBottom: 1
  },
  title: {
    fontSize: 22,
    fontWeight: 'bold',
    color: '#222',
    marginBottom: 16,
  },
  desc: {
    fontSize: 14,
    color: '#888',
    marginTop: 8,
  },
  triangle: {
    width: 0,
    height: 0,
    borderLeftWidth: 18,
    borderRightWidth: 18,
    borderTopWidth: 18,
    borderLeftColor: 'transparent',
    borderRightColor: 'transparent',
    borderTopColor: '#F9F199',
    alignSelf: 'center',
    marginTop: -8,
    zIndex: 1,
  },
});

// 标签选择组件的样式
const tagStyles = StyleSheet.create({
  container: {
    padding: 20,
    backgroundColor: '#FFF8F3',
    marginHorizontal: 16,
    marginBottom: 16,
    borderRadius: 16,
    position: 'relative',
    overflow: 'hidden',
  },
  tagSelectionContainer: {
    width: '100%',
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 20,
    textAlign: 'center',
    color: '#333',
  },
  tagsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'center',
    marginBottom: 30,
  },
  tag: {
    paddingVertical: 12,
    paddingHorizontal: 18,
    margin: 6,
    borderRadius: 20,
    position: 'relative',
    borderWidth: 2,
    borderColor: 'transparent',
  },
  selectedTag: {
    borderColor: '#E59B62',
    borderWidth: 2,
  },
  tagText: {
    color: '#333',
    fontSize: 14,
    fontWeight: '500',
  },
  checkmarkContainer: {
    position: 'absolute',
    top: -8,
    right: -8,
    zIndex: 1,
  },
  checkmark: {
    width: 20,
    height: 20,
    borderRadius: 10,
    backgroundColor: '#E59B62',
    justifyContent: 'center',
    alignItems: 'center',
  },
  checkmarkText: {
    color: 'white',
    fontSize: 12,
    fontWeight: 'bold',
  },
  matchButton: {
    backgroundColor: '#FFD29B',
    padding: 15,
    borderRadius: 8,
    alignItems: 'center',
    marginBottom: 20,
    zIndex: 10,
    elevation: 5,
  },
  matchButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
  selectedCount: {
    textAlign: 'center',
    color: '#666',
    fontSize: 14,
  },
  // 匹配界面样式
  matchingContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: '#FFF8F3',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingTop: 0,
    paddingBottom: 40,
    paddingHorizontal: 20,
  },
  titleContainer: {
    width: '100%',
    alignItems: 'center',
    paddingTop: 5,
    paddingBottom: 10,
    zIndex: 100,
    backgroundColor: '#FFF8F3',
  },
  matchingTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
    textAlign: 'center',
    marginBottom: 4,
  },
  matchingSubtitle: {
    fontSize: 13,
    color: '#666',
    textAlign: 'center',
    marginBottom: 0,
  },
  svgContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    flex: 1,
    width: '100%',
    position: 'relative',
    maxHeight: 350,
    marginTop: 30,
  },
  svgCard: {
    position: 'absolute',
  },
  mergedCard: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  mergedCardContainer: {
    position: 'relative',
    width: 194,
    height: 298,
    alignItems: 'center',
    justifyContent: 'center',
  },
  svgBackground: {
    position: 'absolute',
    top: 0,
    left: 0,
  },
  matchPercentageOverlay: {
    position: 'absolute',
    top: 20,
    left: 20,
    alignItems: 'center',
    justifyContent: 'center',
    width: 60,
    height: 60,
    zIndex: 10,
  },
  percentageNumber: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#74945A',
    marginBottom: 1,
    textAlign: 'center',
  },
  percentageLabel: {
    fontSize: 8,
    color: '#74945A',
    fontWeight: '500',
    textAlign: 'center',
  },
  topRightIcon: {
    position: 'absolute',
    top: 32,
    right: 35,
    zIndex: 10,
  },
  creatIcon: {
    width: 20,
    height: 20,
    tintColor: '#74945A',
  },
  userNamesContainer: {
    position: 'absolute',
    top: 110,
    left: -55,
    right: 10,
    alignItems: 'center',
    zIndex: 10,
  },
  userNames: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#74945A',
    textAlign: 'center',
    width: '100%',
  },
  avatarsContainer: {
    position: 'absolute',
    bottom: 36,
    left: -20,
    right: 0,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 25,
    zIndex: 10,
  },
  avatar: {
    width: 55,
    height: 55,
    borderRadius: 27.5,
    borderWidth: 2,
    borderColor: '#74945A',
  },
  leftAvatar: {
    marginLeft: 13,
  },
  rightAvatar: {
    marginRight: -7,
  },
  // 进入按钮样式
  enterButton: {
    position: 'absolute',
    bottom: 105,
    right: 15,
    backgroundColor: '#FFB366',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.15,
    shadowRadius: 4,
    elevation: 3,
  },
  enterButtonText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: '600',
    textAlign: 'center',
  },
  dotsContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    height: 40,
    width: '100%',
  },
  dot: {
    width: 10,
    height: 10,
    borderRadius: 5,
    backgroundColor: '#FFD29B',
    marginHorizontal: 5,
  },
  // 橘色卡片样式
  orangeCardContainer: {
    position: 'relative',
    width: 177,
    height: 228,
    alignItems: 'center',
    justifyContent: 'center',
  },
  orangeMatchPercentageOverlay: {
    position: 'absolute',
    top: 30,
    left: 15,
    alignItems: 'center',
    justifyContent: 'center',
    width: 50,
    height: 50,
    zIndex: 10,
  },
  orangePercentageNumber: {
    fontSize: 12,
    fontWeight: 'bold',
    color: '#B06509',
    marginBottom: 1,
    textAlign: 'center',
  },
  orangePercentageLabel: {
    fontSize: 7,
    color: '#B06509',
    fontWeight: '500',
    textAlign: 'center',
  },
  orangeTopRightIcon: {
    position: 'absolute',
    top: 22,
    right: 56,
    zIndex: 10,
  },
  orangeCreatIcon: {
    width: 16,
    height: 16,
    tintColor: '#B06509',
  },

  orangeAvatarsContainer: {
    position: 'absolute',
    bottom: 11,
    left: -42,
    right: 0,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 20,
    zIndex: 10,
  },
  orangeAvatar: {
    width: 45,
    height: 45,
    borderRadius: 24,
    borderWidth: 2,
    borderColor: '#B06509',
  },
  // 蓝色卡片样式
  blueCardContainer: {
    position: 'relative',
    width: 190,
    height: 247,
    alignItems: 'center',
    justifyContent: 'center',
  },
  blueMatchPercentageOverlay: {
    position: 'absolute',
    top: 15,
    left: 57,
    alignItems: 'center',
    justifyContent: 'center',
    width: 50,
    height: 50,
    zIndex: 10,
  },
  bluePercentageNumber: {
    fontSize: 12,
    fontWeight: 'bold',
    color: '#436F93',
    marginBottom: 1,
    textAlign: 'center',
  },
  bluePercentageLabel: {
    fontSize: 7,
    color: '#436F93',
    fontWeight: '500',
    textAlign: 'center',
  },
  blueTopRightIcon: {
    position: 'absolute',
    top: 48,
    right: 24,
    zIndex: 10,
  },
  blueCreatIcon: {
    width: 16,
    height: 16,
    tintColor: '#436F93',
  },

  blueAvatarsContainer: {
    position: 'absolute',
    bottom: 29,
    left: 26,
    right: 0,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 25,
    zIndex: 10,
  },
  blueAvatar: {
    width: 45,
    height: 45,
    borderRadius: 24,
    borderWidth: 2,
    borderColor: '#436F93',
  },
});