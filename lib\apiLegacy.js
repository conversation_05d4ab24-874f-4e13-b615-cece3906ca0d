// lib/apiLegacy.js - 兼容性层，保持向后兼容
import { getApiBaseUrl } from './apiConfig';
import { Platform } from 'react-native';

/**
 * 兼容旧版本的API调用
 * 这个文件提供了与旧API文件相同的接口，但内部使用新的统一API系统
 */

// 动态导入 apiServices 以避免循环依赖和模块加载问题
const getApiServices = async () => {
  const { default: apiServices } = await import('./apiServices');
  return apiServices;
};

// 兼容 lib/api.js 中的函数
export const login = async (...args) => {
  const apiServices = await getApiServices();
  return apiServices.auth.login(...args);
};

export const register = async (...args) => {
  const apiServices = await getApiServices();
  return apiServices.auth.register(...args);
};

export const forgotPassword = async (...args) => {
  const apiServices = await getApiServices();
  return apiServices.auth.forgotPassword(...args);
};

export const verifyUser = async (...args) => {
  const apiServices = await getApiServices();
  return apiServices.auth.verifyUser(...args);
};

export const validateUserBasic = async (...args) => {
  const apiServices = await getApiServices();
  return apiServices.auth.validateUserBasic(...args);
};

export const getCurrentUser = async (...args) => {
  const apiServices = await getApiServices();
  return apiServices.auth.getCurrentUser(...args);
};

export const validateToken = async (...args) => {
  const apiServices = await getApiServices();
  return apiServices.auth.validateToken(...args);
};

export const checkLoginStatus = async (...args) => {
  const apiServices = await getApiServices();
  return apiServices.auth.checkLoginStatus(...args);
};

export const requireLogin = async (...args) => {
  const apiServices = await getApiServices();
  return apiServices.auth.requireLogin(...args);
};

export const executeWithLogin = async (...args) => {
  const apiServices = await getApiServices();
  return apiServices.auth.executeWithLogin(...args);
};

export const getUserId = async (...args) => {
  const apiServices = await getApiServices();
  return apiServices.auth.getUserId(...args);
};

export const isGuestMode = async (...args) => {
  const apiServices = await getApiServices();
  return apiServices.auth.isGuestMode(...args);
};

export const testNetworkConnection = async (...args) => {
  const apiServices = await getApiServices();
  return apiServices.auth.testNetworkConnection(...args);
};

export const getTagsByStepId = async (...args) => {
  const apiServices = await getApiServices();
  return apiServices.tag.getTagsByStepId(...args);
};

// 兼容 lib/contentApi.js 中的函数
export const getIndexData = async (...args) => {
  const apiServices = await getApiServices();
  return apiServices.content.getIndexData(...args);
};

export const getFeaturedContentList = async (...args) => {
  const apiServices = await getApiServices();
  return apiServices.content.getFeaturedContentList(...args);
};

export const getHotCourseList = async (...args) => {
  const apiServices = await getApiServices();
  return apiServices.content.getHotCourseList(...args);
};

export const getLatestCourseList = async (...args) => {
  const apiServices = await getApiServices();
  return apiServices.content.getLatestCourseList(...args);
};

export const getRecommendVideoList = async (...args) => {
  const apiServices = await getApiServices();
  return apiServices.content.getRecommendVideoList(...args);
};

export const getRecommendations = async (...args) => {
  const apiServices = await getApiServices();
  return apiServices.content.getRecommendations(...args);
};

// 兼容 lib/userProfile.js 中的函数
export const saveUserTagAndGenerateProfile = async (...args) => {
  const apiServices = await getApiServices();
  return apiServices.profile.saveUserTagAndGenerateProfile(...args);
};

// 兼容 lib/profileApi.js 中的函数
export const saveUserProfile = async (...args) => {
  const apiServices = await getApiServices();
  return apiServices.profile.updateUserProfile(...args);
};

export const fetchUserProfile = async (...args) => {
  const apiServices = await getApiServices();
  return apiServices.profile.getUserProfile(...args);
};

// 兼容旧版本的 sendSelectedIdsToBackend 函数
export const sendSelectedIdsToBackend = async (ids) => {
  console.log(`[${Platform.OS}] sendSelectedIdsToBackend (兼容模式):`, ids);

  try {
    const apiServices = await getApiServices();
    // 使用新的API系统
    const response = await apiServices.profile.saveUserTagAndGenerateProfile({ tagIdList: ids });
    return response;
  } catch (error) {
    console.error(`[${Platform.OS}] sendSelectedIdsToBackend 失败:`, error);
    throw error;
  }
};

// 兼容旧版本的 API_BASE_URL 导出
// 注意：这是一个异步函数，使用时需要 await
export const getApiBaseUrlLegacy = getApiBaseUrl;

// 兼容旧版本的网络配置函数
export {
  saveCustomIP,
  getCustomIP,
  resetNetworkConfig,
  debugNetworkConfig as getNetworkConfigInfo
} from './apiConfig';

/**
 * 迁移提示函数
 * 在控制台输出迁移建议
 */
export const showMigrationTip = (oldFunction, newFunction) => {
  console.warn(`[${Platform.OS}] 迁移提示: 建议使用新的API系统`);
  console.warn(`旧方式: ${oldFunction}`);
  console.warn(`新方式: ${newFunction}`);
  console.warn('详情请查看 lib/apiServices.js');
};

// 导出新的API系统（推荐使用）
export { default as apiServices } from './apiServices';
export { default as apiClient } from './apiClient';
export * from './apiConfig';
