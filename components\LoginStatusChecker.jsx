// components/LoginStatusChecker.jsx - 登录状态检查组件
import React, { useState } from 'react';
import { View, TouchableOpacity, Platform } from 'react-native';
import ThemedText from './ThemedText';
import apiServices from '../lib/apiServices';

const LoginStatusChecker = () => {
  const [loading, setLoading] = useState(false);
  const [statusInfo, setStatusInfo] = useState('');
  const [loginStatus, setLoginStatus] = useState(null);

  // 检查登录状态
  const handleCheckStatus = async () => {
    setLoading(true);
    try {
      console.log(`[${Platform.OS}] 开始检查登录状态`);

      const status = await apiServices.auth.checkLoginStatus();
      const info = `登录状态: ${status.isLoggedIn ? '已登录' : '未登录'}
用户信息: ${status.user ? `${status.user.userAccount} (ID: ${status.user.id})` : '无'}
Token状态: ${status.token ? '有效' : '无效'}
平台: ${Platform.OS}`;

      setLoginStatus(status);
      setStatusInfo(info);

      console.log(`[${Platform.OS}] 登录状态检查完成:`, status);
    } catch (error) {
      console.error(`[${Platform.OS}] 检查登录状态失败:`, error);
      setStatusInfo(`检查失败: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  // 清除登录状态
  const handleClearStatus = async () => {
    setLoading(true);
    try {
      console.log(`[${Platform.OS}] 开始清除登录状态`);

      const success = await apiServices.auth.logout();

      if (success) {
        setStatusInfo('登录状态已清除');
        setLoginStatus({ isLoggedIn: false, user: null, token: null });
      } else {
        setStatusInfo('清除登录状态失败');
      }

      console.log(`[${Platform.OS}] 清除登录状态结果:`, success);
    } catch (error) {
      console.error(`[${Platform.OS}] 清除登录状态失败:`, error);
      setStatusInfo(`清除失败: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  return (
    <View style={{
      backgroundColor: '#f8f9fa',
      padding: 20,
      margin: 10,
      borderRadius: 8,
      borderWidth: 1,
      borderColor: '#dee2e6'
    }}>
      <ThemedText style={{ fontSize: 16, fontWeight: 'bold', marginBottom: 15 }}>
        登录状态检查工具
      </ThemedText>

      {/* 操作按钮 */}
      <View style={{ flexDirection: 'row', marginBottom: 15 }}>
        <TouchableOpacity
          onPress={handleCheckStatus}
          disabled={loading}
          style={[
            buttonStyle,
            { 
              backgroundColor: loading ? '#6c757d' : '#007bff',
              marginRight: 10,
              flex: 1
            }
          ]}
        >
          <ThemedText style={buttonTextStyle}>
            {loading ? '检查中...' : '检查状态'}
          </ThemedText>
        </TouchableOpacity>

        <TouchableOpacity
          onPress={handleClearStatus}
          disabled={loading}
          style={[
            buttonStyle,
            { 
              backgroundColor: loading ? '#6c757d' : '#dc3545',
              flex: 1
            }
          ]}
        >
          <ThemedText style={buttonTextStyle}>
            {loading ? '清除中...' : '清除状态'}
          </ThemedText>
        </TouchableOpacity>
      </View>

      {/* 状态显示 */}
      {loginStatus && (
        <View style={{
          backgroundColor: loginStatus.isLoggedIn ? '#d4edda' : '#f8d7da',
          padding: 12,
          borderRadius: 5,
          marginBottom: 10,
          borderLeftWidth: 4,
          borderLeftColor: loginStatus.isLoggedIn ? '#28a745' : '#dc3545'
        }}>
          <ThemedText style={{
            color: loginStatus.isLoggedIn ? '#155724' : '#721c24',
            fontWeight: 'bold',
            marginBottom: 5
          }}>
            {loginStatus.isLoggedIn ? '✅ 已登录' : '❌ 未登录'}
          </ThemedText>
          
          {loginStatus.user && (
            <View>
              <ThemedText style={{
                color: '#155724',
                fontSize: 12,
                marginBottom: 2
              }}>
                用户账号: {loginStatus.user.userAccount || '未知'}
              </ThemedText>
              <ThemedText style={{
                color: '#155724',
                fontSize: 12,
                marginBottom: 2
              }}>
                用户ID: {loginStatus.user.id || '未知'}
              </ThemedText>
            </View>
          )}
          
          <ThemedText style={{
            color: loginStatus.isLoggedIn ? '#155724' : '#721c24',
            fontSize: 12
          }}>
            Token状态: {loginStatus.token ? '有效' : '无效'}
          </ThemedText>
        </View>
      )}

      {/* 详细信息 */}
      {statusInfo && (
        <View style={{
          backgroundColor: '#ffffff',
          padding: 12,
          borderRadius: 5,
          borderWidth: 1,
          borderColor: '#dee2e6'
        }}>
          <ThemedText style={{
            fontSize: 11,
            fontFamily: 'monospace',
            color: '#495057',
            lineHeight: 16
          }}>
            {statusInfo}
          </ThemedText>
        </View>
      )}

      {/* 使用说明 */}
      <View style={{
        backgroundColor: '#e9ecef',
        padding: 10,
        borderRadius: 5,
        marginTop: 10
      }}>
        <ThemedText style={{ fontSize: 12, color: '#6c757d' }}>
          💡 使用说明：
        </ThemedText>
        <ThemedText style={{ fontSize: 11, color: '#6c757d', marginTop: 5 }}>
          • 检查状态：查看当前登录状态和用户信息{'\n'}
          • 清除状态：清除本地保存的登录信息{'\n'}
          • 如果保存标签时出现401错误，请先检查登录状态
        </ThemedText>
      </View>
    </View>
  );
};

const buttonStyle = {
  padding: 12,
  borderRadius: 6,
  alignItems: 'center',
  justifyContent: 'center'
};

const buttonTextStyle = {
  color: 'white',
  fontWeight: 'bold',
  fontSize: 14
};

export default LoginStatusChecker;
