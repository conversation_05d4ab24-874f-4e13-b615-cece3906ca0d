import { Keyboard, StyleSheet, Text, Pressable, View, TouchableOpacity, Image, ScrollView } from 'react-native'
import { router } from 'expo-router'
import { useState } from 'react'

import ThemedView from '../../components/ThemedView'
import ThemedText from '../../components/ThemedText'
import Spacer from '../../components/Spacer'
import ThemedTextInput from "../../components/ThemedTextInput"
import GradientGlassButton from '../../components/GradientGlassButton'
import CustomCheckbox from '../../components/CustomCheckbox'
import { Colors } from '../../constants/Colors'

const ForgotPassword = () => {
  const [phoneNumber, setPhoneNumber] = useState("")
  const [verificationCode, setVerificationCode] = useState("")
  const [newPassword, setNewPassword] = useState("")
  const [error, setError] = useState(null)
  const [showPassword, setShowPassword] = useState(false)
  const [agreedToTerms, setAgreedToTerms] = useState(false) // 新增：用户协议同意状态

  const handleSubmit = async () => {
    setError(null)

    // 检查是否同意用户协议
    if (!agreedToTerms) {
      setError("请先阅读并同意《用户协议》和《隐私授权》")
      return
    }

    if (!phoneNumber || !verificationCode || !newPassword) {
      setError("请填写所有字段")
      return
    }

    try {
      // 这里可以添加重置密码的逻辑
      console.log('重置密码:', { phoneNumber, verificationCode, newPassword })
      
      // 重置成功后跳转到登录页面
      router.replace('/(auth)/login')
    } catch (error) {
      setError(error.message)
    }
  }

  // 获取验证码的处理函数
  const handleGetVerificationCode = () => {
    if (!phoneNumber) {
      setError("请先输入手机号")
      return
    }
    // 这里可以添加获取验证码的逻辑
    console.log('获取验证码:', phoneNumber)
  }

  return (
    <Pressable onPress={Keyboard.dismiss} style={{ flex: 1 }}>
      <ThemedView style={styles.container} safe={false}>
        <ScrollView
          style={styles.scrollView}
          contentContainerStyle={styles.scrollContent}
          showsVerticalScrollIndicator={false}
          keyboardShouldPersistTaps="handled"
        >
          <Spacer height={15} />

          {/* 返回按钮 */}
          <TouchableOpacity style={styles.backButton}  onPress={() => router.push('/(auth)/login')}>
            <Image source={require('../../assets/Arrows_left.png')} style={styles.backButtonIcon} />
          </TouchableOpacity>

          <Spacer height={30} />

          {/* 标题 */}
          <View style={styles.titleContainer}>
            <ThemedText title={true} style={styles.title}>
              忘记密码
            </ThemedText>
      
          </View>

          <Spacer height={60} />

          {/* 白色表单容器 */}
          <View style={styles.whiteContainer}>
            {/* 表单 */}
            <View style={styles.formContainer}>
          <ThemedText style={styles.fieldLabel}>手机号</ThemedText>
          <View style={styles.inputContainer}>
            <Image source={require('../../assets/Auth_image/People_number.png')} style={styles.inputIcon} />
            <ThemedTextInput
              style={[styles.input, styles.inputWithIcon]}
              placeholder="请输入手机号"
              value={phoneNumber}
              onChangeText={setPhoneNumber}
              keyboardType="phone-pad"
            />
          </View>

          <Spacer height={20} />
          <ThemedText style={styles.fieldLabel}>验证码</ThemedText>
          <View style={styles.inputContainer}>
            <Image source={require('../../assets/Auth_image/Key.png')} style={styles.inputIcon} />
            <ThemedTextInput
              style={[styles.input, styles.inputWithIconAndButton]}
              placeholder="请输入验证码"
              value={verificationCode}
              onChangeText={setVerificationCode}
              keyboardType="number-pad"
            />
            <TouchableOpacity style={styles.getCodeButton} onPress={handleGetVerificationCode}>
              <Text style={styles.getCodeText}>获取验证码</Text>
            </TouchableOpacity>
          </View>

          <Spacer height={10} />
         
          <View style={styles.passwordContainer}>
            <Image source={require('../../assets/Auth_image/Lock.png')} style={styles.inputIcon} />
            <ThemedTextInput
              style={[styles.input, styles.passwordInput]}
              placeholder="请设置新的登录密码"
              value={newPassword}
              onChangeText={setNewPassword}
              secureTextEntry={!showPassword}
            />
            <TouchableOpacity
              style={styles.eyeButton}
              onPress={() => setShowPassword(!showPassword)}
            >
              <Image
                source={showPassword ? require('../../assets/Auth_image/Eyes_open.png') : require('../../assets/Auth_image/Eyes_close.png')}
                style={styles.eyeIcon}
              />
            </TouchableOpacity>
          </View>
          
          <Text style={styles.passwordHint}>8-16位，（大写字母+小写字母+数字）密码</Text>
        </View>

        <Spacer height={10} />

        {/* 确认更换按钮 */}
        <GradientGlassButton
          title="确认更换"
          onPress={handleSubmit}
          style={[styles.confirmButton, !agreedToTerms && styles.disabledButton]}
          disabled={!agreedToTerms}
        />

        {/* 用户协议 */}
        <View style={styles.agreementContainer}>
          <CustomCheckbox
            checked={agreedToTerms}
            onPress={() => setAgreedToTerms(!agreedToTerms)}
            checkedColor="#FFC885"
            size={15}
            style={styles.checkbox}
          />
          <Text style={styles.agreementText}>
              我已阅读并同意
              <Text style={styles.agreementLink}>《用户协议》</Text>
              和
              <Text style={styles.agreementLink}>《隐私授权》</Text>
          </Text>
        </View>

            <Spacer />
            {error && <Text style={styles.error}>{error}</Text>}

          </View>
          {/* 白色容器结束 */}



        </ScrollView>

      </ThemedView>
    </Pressable>
  )
}

export default ForgotPassword

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
    minHeight: '100%', // 确保内容至少铺满整个屏幕
    paddingHorizontal: 10,
    paddingTop: 20,
    paddingBottom: 0, // 移除底部padding
  },
  backButton: {
    alignSelf: 'flex-start',
    marginLeft: 10,
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
  backButtonIcon: {
    width: 24,
    height: 24,
  },
  titleContainer: {
    alignItems: 'flex-start',
    paddingHorizontal: 20,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    marginBottom: 10,
    color: Colors.boldText,
  },
  whiteContainer: {
    backgroundColor: '#FFFFFF',
    borderRadius: 24,
    marginHorizontal: 16,
    paddingHorizontal: 20,
    paddingVertical: 30,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.12,
    shadowRadius: 12,
    elevation: 6,
    marginBottom: 20,
  },
  formContainer: {
    paddingHorizontal: 0,
  },
  fieldLabel: {
    fontSize: 16,
    color: Colors.boldText,
    marginBottom: 8,
    fontWeight: '500',
  },
  inputContainer: {
    position: 'relative',
    flexDirection: 'row',
    alignItems: 'center',
  },
  inputIcon: {
    position: 'absolute',
    left: 16,
    width: 20,
    height: 20,
    zIndex: 1,
    marginRight: 12,
  },
  input: {
    backgroundColor: '#F5F5F5',
    borderRadius: 36,
    paddingHorizontal: 20,
    paddingVertical: 15,
    fontSize: 16,
    color: Colors.normalText,
    padding: 0,
  },
  inputWithIcon: {
    paddingLeft: 45,
    paddingRight: 20,
    paddingVertical: 15,
    flex: 1,
  },
  inputWithIconAndButton: {
    paddingLeft: 45,
    paddingRight: 100, // 为获取验证码按钮留出空间
    paddingVertical: 15,
    flex: 1,
  },
  passwordContainer: {
    position: 'relative',
    flexDirection: 'row',
    alignItems: 'center',
  },
  passwordInput: {
    paddingLeft: 45,
    paddingRight: 56,
    paddingVertical: 15,
    flex: 1,
  },
  eyeButton: {
    position: 'absolute',
    right: 20,
    top: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 4,
  },
  eyeIcon: {
    width: 20,
    height: 20,
  },
  verificationInput: {
    flex: 1,
    marginRight: 12,
    paddingLeft: 52,
  },
  getCodeButton: {
    paddingHorizontal: 16,
    paddingVertical: 16,
  },
  getCodeText: {
    color: Colors.button,
    fontSize: 16,
    fontWeight: '500',
  },
  passwordHint: {
    fontSize: 12,
    color: Colors.normalText,
    marginTop: 8,
    paddingLeft: 16,
  },
  getCodeButton: {
    position: 'absolute',
    right: 15,
    top: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 8,
  },
  getCodeText: {
    color: '#7A3C10',
    fontSize: 12,
    fontWeight: '600',
  },
  confirmButton: {
    marginBottom: 20,
  },
  agreementContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 20,
  },
  checkbox: {
    marginRight: 12,
  },
  disabledButton: {
    opacity: 0.5,
  },
  agreementText: {
    fontSize: 12,
    color: Colors.normalText,
    flex: 1,
  },
  agreementLink: {
    color: '#7A3C10',
    fontSize: 12,
  },
  thirdPartyContainer: {
    alignItems: 'center',
    marginBottom: 40,
  },
  thirdPartyTitle: {
    fontSize: 14,
    color: Colors.normalText,
    marginBottom: 20,
  },
  thirdPartyButtons: {
    flexDirection: 'row',
    justifyContent: 'center',
  },
  thirdPartyButton: {
    width: 40,
    height: 40,
    borderRadius: 25,
    backgroundColor: Colors.button, // 使用按钮颜色常量
    justifyContent: 'center',
    alignItems: 'center',
    marginHorizontal: 20,
  },
  thirdPartyIcon: {
    width: 24,
    height: 24,
    resizeMode: 'contain',
  },
  forthPartyIcon: {
    width: 28,
    height: 28,
    resizeMode: 'contain',
  },
  error: {
    color: Colors.warning,
    padding: 10,
    backgroundColor: '#f5c1c8',
    borderColor: Colors.warning,
    borderWidth: 1,
    borderRadius: 6,
    marginHorizontal: 10,
    textAlign: 'center',
  }
})
