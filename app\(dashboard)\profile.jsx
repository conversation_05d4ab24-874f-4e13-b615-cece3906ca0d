import { StyleSheet, View, Image, TouchableOpacity, FlatList, ScrollView, Animated, Dimensions } from 'react-native'
import { useUser } from '../../hooks/useUser'
import { useGuestPermission } from '../../hooks/useGuestPermission'
import GuestPermissionModal from '../../components/GuestPermissionModal'
import Spacer from "../../components/Spacer"
import ThemedText from "../../components/ThemedText"
import ThemedView from "../../components/ThemedView"
import { useRouter } from 'expo-router'
import { useEffect, useRef } from 'react'
import { BlurView } from 'expo-blur'
import { LinearGradient } from 'expo-linear-gradient'

const profilePic = require('../../assets/qweqw.png')
const settingsIcon = require('../../assets/Profile_image/Settings.png')
const dogIcon = require('../../assets/dog.png')
const likeIcon = require('../../assets/Profile_image/Like.png')
const bookIcon = require('../../assets/Profile_image/Book.png')
const errorIcon = require('../../assets/Profile_image/Error.png')
const bgImg = require('../../assets/Profile_image/bgImg.png')
const arrowIcon = require('../../assets/icon.png')
const arrowRightIcon = require('../../assets/Arrows_right.png')

// 常用功能区图标
const testDatabaseIcon = require('../../assets/Profile_image/TestDatabase.png')
const courseIcon = require('../../assets/Profile_image/Course.png')
const shareIcon = require('../../assets/Profile_image/Share.png')
const pointsIcon = require('../../assets/Profile_image/Points.png')
const helpIcon = require('../../assets/Profile_image/Help.png')

const Profile = () => {
  const router = useRouter()
  const { user: currentUser } = useUser()
  const {
    wrapGuestAction,
    showPermissionModal,
    currentFeature,
    handleRegister,
    closePermissionModal
  } = useGuestPermission()

  // 根据用户类型显示不同数据
  const displayUser = currentUser?.isGuest
    ? {
        name: '游客用户',
        score: 0,
        fans: 0,
        follow: 0,
        avatar: profilePic
      }
    : {
        name: currentUser?.username || '用户',
        score: 520,
        fans: 67,
        follow: 118,
        avatar: profilePic
      }

  const screenWidth = Dimensions.get('window').width

  // 滚动动画值
  const scrollY = useRef(new Animated.Value(0)).current

  // 遮罩透明度动画值
  const overlayOpacity = scrollY.interpolate({
    inputRange: [0, 100, 200],
    outputRange: [0, 0.5, 0.9],
    extrapolate: 'clamp'
  })

  useEffect(() => {
    console.log('到达profile页面');
  }, []);

  return (
    <ThemedView style={styles.container}>
      {/* 安全视图 */}
      <View style={styles.safeArea} />

      <Animated.ScrollView
        contentContainerStyle={{
          paddingTop: 60,
          paddingBottom: 120,
          paddingHorizontal: 16,
        }}
        showsVerticalScrollIndicator={false}
        onScroll={Animated.event(
          [{ nativeEvent: { contentOffset: { y: scrollY } } }],
          { useNativeDriver: false }
        )}
        scrollEventThrottle={16}
        style={styles.scrollView}
      >
        {/* 背景图容器 - 完全填充宽度并覆盖顶部空隙 */}
        <View style={styles.backgroundImageContainer}>
          <Image source={bgImg} style={[styles.scrollableBackgroundImage, { width: screenWidth, resizeMode: 'cover' }]} />

          {/* 动态遮罩层 - 与背景图完全重合 */}
          <Animated.View style={[
            styles.overlayContainer,
            {
              opacity: overlayOpacity,
              width: screenWidth,
            }
          ]}>
            <LinearGradient
              colors={['rgba(252, 241, 228, 0.9)', 'rgba(252, 241, 228, 0.6)', 'rgba(252, 241, 228, 0.2)']}
              locations={[0, 0.6, 1]}
              style={[styles.overlayGradient, { width: screenWidth }]}
            />
          </Animated.View>

          {/* 顶部右上角设置和dog图标 - 可滚动 */}
          <View style={styles.topRightIcons}>
            <Image source={dogIcon} style={styles.dogIcon} />
            <TouchableOpacity
              onPress={wrapGuestAction(() => {
                router.push('/profile-settings');
              }, 'settings')}
              style={styles.settingsBtnTop}
            >
              <Image source={settingsIcon} style={styles.settingsIconTop} />
            </TouchableOpacity>
          </View>
        </View>

        {/* 内容区域 */}
        <View style={[styles.contentContainer]}>
          {/* 顶部信息卡片 - 毛玻璃效果 */}
          <BlurView intensity={30} style={styles.infoCard}>
            <LinearGradient
              colors={['rgba(252, 241, 228, 0.7)', 'rgba(255, 210, 155, 0.5)']}
              style={styles.infoCardGradient}
            >
              <View style={{ flexDirection: 'row', alignItems: 'center', flex: 1 }}>
                <Image source={profilePic} style={styles.avatar} />
                <View style={{ marginLeft: 12, flex: 1 }}>
                  <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                    <ThemedText style={styles.name}>{displayUser.name}</ThemedText>
                    <TouchableOpacity onPress={wrapGuestAction(() => {
                      console.log('编辑个人资料');
                    }, 'profile-edit')}>
                      <ThemedText style={styles.editIcon}>✎</ThemedText>
                    </TouchableOpacity>
                  </View>
                  <ThemedText style={styles.score}>
                    {currentUser?.isGuest ? '游客模式 - 注册后可获得积分' : `当前积分：${displayUser.score}`}
                  </ThemedText>
                </View>
                <View style={styles.statBox}>
                  <ThemedText style={styles.statNum}>{displayUser.fans}</ThemedText>
                  <ThemedText style={styles.statLabel}>粉丝</ThemedText>
                </View>
                <View style={styles.statBox}>
                  <ThemedText style={styles.statNum}>{displayUser.follow}</ThemedText>
                  <ThemedText style={styles.statLabel}>关注</ThemedText>
                </View>
              </View>
            </LinearGradient>
          </BlurView>
          {/* 四功能区横向盒子 */}
          <View style={styles.featureBar}>
            <View style={styles.featureBarItem}>
              <Image source={likeIcon} style={styles.featureBarIcon} />
              <ThemedText style={styles.featureBarText}>点赞</ThemedText>
            </View>
            <View style={styles.featureBarItem}>
              <Image source={dogIcon} style={styles.featureBarIcon} />
              <ThemedText style={styles.featureBarText}>学习画像</ThemedText>
            </View>
            <View style={styles.featureBarItem}>
              <Image source={bookIcon} style={styles.featureBarIcon} />
              <ThemedText style={styles.featureBarText}>笔记本</ThemedText>
            </View>
            <View style={styles.featureBarItem}>
              <Image source={errorIcon} style={styles.featureBarIcon} />
              <ThemedText style={styles.featureBarText}>错题库</ThemedText>
            </View>
          </View>
          {/* 数据分析区 */}
          <View style={styles.sectionCard}>
            <ThemedText style={styles.sectionTitle}>数据分析</ThemedText>
            {/* 这里可放置图表等内容 */}
            <View style={styles.analysisPlaceholder} />
          </View>
          {/* 学习记录区 */}
          <View style={styles.sectionCard}>
            <ThemedText style={styles.sectionTitle}>学习记录</ThemedText>
            <LinearGradient
              colors={['#D8F0D0', '#FFD29B']}
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 0 }}
              style={styles.studyRecordCard}
            >
              <View style={styles.studyRecordLeft} />
              <View style={{ flex: 1, marginLeft: 10 }}>
                <ThemedText style={styles.studyRecordTitle}>How is Life in Paradise ?</ThemedText>
                <ThemedText style={styles.studyRecordDesc}>继续上次记录学习</ThemedText>
              </View>
              <TouchableOpacity style={styles.studyContinueBtn}>
                <ThemedText style={styles.studyContinueText}>继续学习</ThemedText>
              </TouchableOpacity>
            </LinearGradient>
          </View>
          {/* 常用功能区 */}
          <View style={styles.sectionCard}>
            <View style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 8 }}>
              <ThemedText style={styles.sectionTitle}>常用功能</ThemedText>
            </View>
            <View style={[styles.featureList, { backgroundColor: '#FFEEDB' }]}>
              <FeatureItem iconSource={testDatabaseIcon} label="我的题库" />
              <FeatureItem iconSource={courseIcon} label="我的课程" />
              <FeatureItem iconSource={shareIcon} label="分享APP" />
              <FeatureItem iconSource={pointsIcon} label="积分兑换" />
              <FeatureItem iconSource={helpIcon} label="帮助与反馈" isLast />
            </View>
          </View>
        </View>
      </Animated.ScrollView>

      {/* 游客权限提示弹窗 */}
      <GuestPermissionModal
        visible={showPermissionModal}
        onClose={closePermissionModal}
        onRegister={handleRegister}
        featureName={currentFeature.name}
        description={currentFeature.description}
      />
    </ThemedView>
  )
}

const FeatureItem = ({ iconSource, label, isLast }) => (
  <View style={[styles.featureItem, isLast && { borderBottomWidth: 0 }]}>
    <Image source={iconSource} style={styles.featureIconImage} tintColor="#666" />
    <ThemedText style={styles.featureLabel}>{String(label)}</ThemedText>
    <View style={{ flex: 1 }} />
    <Image source={arrowRightIcon} style={styles.featureArrowIcon} />
  </View>
)

export default Profile

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FCF1E4',
    position: 'relative'
  },
  safeArea: {
    height: 50,
    backgroundColor: '#FCF1E4',
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    zIndex: 15,
  },
  overlayContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    height: 232, // 匹配背景图高度
    zIndex: 8, // 在背景图之上，但在图标之下
    pointerEvents: 'none', // 不阻挡触摸事件
    // width 通过内联样式设置为屏幕宽度，确保与背景图一致
  },
  overlayGradient: {
    height: 232, // 匹配背景图高度
    // width 通过内联样式设置为屏幕宽度，确保与背景图一致
  },
  scrollView: {
    flex: 1,
  },
  backgroundImageContainer: {
    height: 232, // 200 + 32 覆盖顶部空隙
    marginTop: -32, // 向上偏移32px覆盖顶部空隙
    marginLeft: -16,
    marginRight: -16,
    overflow: 'hidden',
    alignItems: 'center',
    position: 'relative',
  },
  scrollableBackgroundImage: {
    height: 232, // 匹配容器高度
    // width 通过内联样式设置为屏幕宽度
  },
  contentContainer: {
    // 内容区域，padding由ScrollView提供
  },
  topRightIcons: {
    position: 'absolute',
    right: 34, // 调整右边距，因为容器有负边距
    top: 50, // 18 + 32 调整位置，因为背景图向上移动了32px
    flexDirection: 'row',
    alignItems: 'center',
    zIndex: 10, // 在遮罩之上，确保始终可见
  },
  dogIcon: {
    width: 35,
    height: 40,
    marginRight: 10,
  },
  settingsBtnTop: {
    padding: 4,
  },
  settingsIconTop: {
    width: 24,
    height: 24,
  },
  infoCard: {
    borderRadius: 18,
    marginBottom: 18,
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOpacity: 0.1,
    shadowRadius: 10,
    elevation: 5,
    marginTop: -140, // 负边距让卡片覆盖在背景图上
    zIndex: 2,
    position: 'relative',
    marginHorizontal: 0, // 移除横向边距，因为ScrollView已有padding
  },
  infoCardGradient: {
    padding: 18,
    flexDirection: 'row',
    alignItems: 'center',
  },
  avatar: { width: 56, height: 56, borderRadius: 28, backgroundColor: '#bbb' },
  name: { fontSize: 18, color: '#222', fontWeight: 'bold', marginRight: 6 },
  editIcon: { fontSize: 16, color: '#bbb', marginLeft: 2 },
  score: { fontSize: 13, color: '#888', marginTop: 2 },
  statBox: { alignItems: 'center', marginLeft: 18 },
  statNum: { fontSize: 16, color: '#222', fontWeight: 'bold' },
  statLabel: { fontSize: 12, color: '#000000' },
  settingsBtn: { marginLeft: 12, padding: 4 },
  settingsIcon: { width: 24, height: 24 },
  sectionCard: {
    borderRadius: 16,
    marginBottom: 18,
  },
  sectionTitle: { fontSize: 15, color: '#222', fontWeight: 'bold', marginBottom: 8 },
  analysisPlaceholder: {
    backgroundColor: '#FFD29B',
    borderRadius: 12,
    height: 170,
    marginTop: 8,
    marginBottom: 4,
  },
  studyRecordCard: {
    borderRadius: 12,
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    marginTop: 8,
  },
  studyRecordLeft: {
    width: 40, height: 40, borderRadius: 8, backgroundColor: '#bbb',
  },
  studyRecordTitle: { fontSize: 14, color: '#222', fontWeight: 'bold' },
  studyRecordDesc: { fontSize: 12, color: '#888', marginTop: 2 },
  studyContinueBtn: {
    backgroundColor: '#FFB87E',
    borderRadius: 16,
    paddingHorizontal: 16,
    paddingVertical: 6,
    marginLeft: 8,
  },
  studyContinueText: { color: '#222', fontSize: 13, fontWeight: 'bold', marginLeft: 10 },
  featureList: {
    borderRadius: 12,
    padding: 8,
    marginTop: 4,
  },
  featureItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 10,
    borderBottomWidth: 1,
    borderBottomColor: '#bbb',
  },
  featureIcon: { fontSize: 18, marginRight: 12, color: '#bbb', marginLeft: 8 },
  featureIconImage: {
    width: 24,
    height: 24,
    marginRight: 12,
    marginLeft: 8,
  },
  featureLabel: { fontSize: 15, color: '#888' },
  featureArrowIcon: { width: 20, height: 20, marginLeft: 8 },
  featureBar: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: '#FFEEDB',
    borderRadius: 16,
    marginBottom: 18,
    paddingVertical: 18,
    paddingHorizontal: 8,
    // 阴影效果
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.15,
    shadowRadius: 8,
    elevation: 6,
  },
  featureBarItem: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  featureBarIcon: {
    width: 28,
    height: 28,
    marginBottom: 6,
  },
  featureBarText: {
    fontSize: 14,
    color: '#000000',
  },
});
