// lib/apiConfig.js - 统一的API配置系统
import { Platform } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';

// 统一的网络配置
const NETWORK_CONFIG = {
  development: {
    // 开发机器IP地址 - 统一配置点
    DEV_IP: '**************',
    PORT: '8080',

    // 各平台配置
    web: {
      baseUrl: 'http://localhost:8080',
      authUrl: 'http://localhost:8080/api/auth'
    },
    android: {
      // Android模拟器
      emulator: {
        baseUrl: 'http://********:8080',
        authUrl: 'http://********:8080/api/auth'
      },
      // Android真机
      device: {
        baseUrl: 'http://**************:8080',
        authUrl: 'http://**************:8080/api/auth'
      }
    },
    ios: {
      // iOS模拟器
      simulator: {
        baseUrl: 'http://localhost:8080',
        authUrl: 'http://localhost:8080/api/auth'
      },
      // iOS真机
      device: {
        baseUrl: 'http://**************:8080',
        authUrl: 'http://**************:8080/api/auth'
      }
    }
  },
  production: {
    baseUrl: 'https://your-backend-api.com',
    authUrl: 'https://your-backend-api.com/api/auth'
  }
};

// 存储键名
export const STORAGE_KEYS = {
  AUTH_TOKEN: 'auth_token',
  CUSTOM_IP: 'custom_ip_address',
  USER_DATA: 'user_data',
  PROFILE_SETUP_COMPLETED: 'profileSetupCompleted'
};

/**
 * 获取当前环境的基础URL配置
 */
const getCurrentConfig = async () => {
  if (!__DEV__) {
    return NETWORK_CONFIG.production;
  }

  // 检查是否有自定义IP
  const customIP = await getCustomIP();
  if (customIP) {
    return {
      baseUrl: `http://${customIP}:${NETWORK_CONFIG.development.PORT}`,
      authUrl: `http://${customIP}:${NETWORK_CONFIG.development.PORT}/api/auth`
    };
  }

  const config = NETWORK_CONFIG.development;
  
  if (Platform.OS === 'web') {
    return config.web;
  } else if (Platform.OS === 'android') {
    // 默认使用真机配置，可以根据需要调整
    return config.android.device;
  } else if (Platform.OS === 'ios') {
    // 默认使用真机配置，可以根据需要调整
    return config.ios.device;
  }
  
  // 默认配置
  return config.web;
};

/**
 * 获取API基础URL
 */
export const getApiBaseUrl = async () => {
  const config = await getCurrentConfig();
  return `${config.baseUrl}/api`;
};

/**
 * 获取认证API URL
 */
export const getAuthApiUrl = async () => {
  const config = await getCurrentConfig();
  return config.authUrl;
};

/**
 * 获取图片基础URL
 */
export const getImageBaseUrl = async () => {
  const config = await getCurrentConfig();
  return config.baseUrl;
};

/**
 * 保存自定义IP地址
 */
export const saveCustomIP = async (ip) => {
  try {
    await AsyncStorage.setItem(STORAGE_KEYS.CUSTOM_IP, ip);
    console.log(`[${Platform.OS}] 保存自定义IP:`, ip);
    return true;
  } catch (error) {
    console.error(`[${Platform.OS}] 保存IP失败:`, error);
    return false;
  }
};

/**
 * 获取自定义IP地址
 */
export const getCustomIP = async () => {
  try {
    const ip = await AsyncStorage.getItem(STORAGE_KEYS.CUSTOM_IP);
    return ip;
  } catch (error) {
    console.error(`[${Platform.OS}] 获取自定义IP失败:`, error);
    return null;
  }
};

/**
 * 重置网络配置
 */
export const resetNetworkConfig = async () => {
  try {
    await AsyncStorage.removeItem(STORAGE_KEYS.CUSTOM_IP);
    console.log(`[${Platform.OS}] 网络配置已重置`);
    return true;
  } catch (error) {
    console.error(`[${Platform.OS}] 重置网络配置失败:`, error);
    return false;
  }
};

/**
 * 获取当前网络配置信息（用于调试）
 */
export const getNetworkConfigInfo = async () => {
  const config = await getCurrentConfig();
  const customIP = await getCustomIP();
  
  return {
    platform: Platform.OS,
    environment: __DEV__ ? 'development' : 'production',
    customIP,
    currentConfig: config,
    apiBaseUrl: await getApiBaseUrl(),
    authApiUrl: await getAuthApiUrl(),
    imageBaseUrl: await getImageBaseUrl()
  };
};

/**
 * 调试网络配置
 */
export const debugNetworkConfig = async () => {
  const info = await getNetworkConfigInfo();
  console.log(`[${Platform.OS}] 当前网络配置:`, info);
  return info;
};

// 导出开发IP（向后兼容）
export const getDevIP = () => NETWORK_CONFIG.development.DEV_IP;
