import React from 'react';
import { View, StyleSheet, ScrollView, TouchableOpacity, Image } from 'react-native';
import { useRouter } from 'expo-router';
import ThemedView from '../../components/ThemedView';
import ThemedText from '../../components/ThemedText';
import { Colors } from '../../constants/Colors';
import { shadowPresets } from '../../utils/shadowUtils';

const SynchronizedText = () => {
  const router = useRouter();

  // 正在学习的教材
  const currentBooks = [
    {
      id: 1,
      image: require('../../assets/Index_image/TextbookTwo.png'),
      title: 'VOGUE',
      category: '时尚杂志'
    },
    {
      id: 2,
      image: require('../../assets/Index_image/TextbookPhotoSeven.png'),
      title: 'Black French Girl',
      category: '时尚摄影'
    },
    {
      id: 3,
      image: require('../../assets/Index_image/TextbookPhotoFour.png'),
      title: 'Design Book',
      category: '设计教材'
    }
  ];

  // 同步教材分类
  const textbookCategories = [
    {
      id: 1,
      image: require('../../assets/Index_image/TextbookThree.png'),
      title: '英语写作流程',
      category: '英语学习'
    },
    {
      id: 2,
      image: require('../../assets/Index_image/TextbookOne.png'),
      title: '交际英语',
      category: '口语练习'
    },
    {
      id: 3,
      image: require('../../assets/Index_image/TextbookFive.png'),
      title: '文化创意与设计服务',
      category: '创意设计'
    },
    {
      id: 4,
      image: require('../../assets/Index_image/TextbookSix.png'),
      title: '动画角色设计方法与实践',
      category: '动画设计'
    }
  ];

  return (
    <ThemedView style={styles.container}>
      {/* 安全视图 */}
      <View style={styles.safeArea} />

      <ScrollView style={styles.content} contentContainerStyle={{ paddingTop: 60 }} showsVerticalScrollIndicator={false}>
        {/* 头部导航 */}
        <View style={styles.header}>
          <TouchableOpacity onPress={() => router.back()}>
            <Image source={require('../../assets/FrameTwo.png')} style={[styles.backIcon, { width: 22, height: 22 }]} />
          </TouchableOpacity>
          <ThemedText style={styles.headerTitle}>同步教材</ThemedText>
          <View style={styles.headerRight} />
        </View>
        {/* 正在学习部分 */}
        <View style={styles.section}>
          <ThemedText style={styles.sectionTitle}>正在学习</ThemedText>
          <ScrollView
            horizontal
            showsHorizontalScrollIndicator={false}
            style={styles.horizontalScroll}
          >
            {currentBooks.map((book) => (
              <TouchableOpacity
                key={book.id}
                style={styles.currentBookCard}
                onPress={() => {
                  if (book.id === 2) { // TextbookPhotoSeven.png 是第二个
                    router.push('/Today/SynchronizedTextbooks');
                  }
                }}
              >
                <Image source={book.image} style={styles.currentBookImage} />
              </TouchableOpacity>
            ))}
          </ScrollView>
        </View>

        {/* 同步教材部分 */}
        <View style={styles.section}>
          <ThemedText style={styles.sectionTitle}>同步教材</ThemedText>
          <View style={styles.textbookGrid}>
            {textbookCategories.map((book) => (
              <TouchableOpacity key={book.id} style={styles.textbookCard}>
                <Image source={book.image} style={styles.textbookImage} />
                <ThemedText style={styles.textbookTitle}>{book.title}</ThemedText>
              </TouchableOpacity>
            ))}
          </View>
        </View>
      </ScrollView>
    </ThemedView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFF8F3',
  },
  safeArea: {
    height: 50,
    backgroundColor: '#FFF8F3',
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    zIndex: 15,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingTop: 10,
    paddingBottom: 16,
    backgroundColor: '#FFF8F3',
    marginTop: 0,
    marginBottom: 4,
  },
  backIcon: {
    fontSize: 24,
    color: '#333',
    fontWeight: 'bold',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.light.title,
  },
  headerRight: {
    width: 24,
  },
  content: {
    flex: 1,
    paddingHorizontal: 16,
  },
  section: {
    marginBottom: 32,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 16,
  },
  horizontalScroll: {
    marginHorizontal: -16,
    paddingHorizontal: 16,
  },
  currentBookCard: {
    marginRight: 12,
    borderRadius: 12,
    overflow: 'hidden',
  },
  currentBookImage: {
    width: 120,
    height: 160,
    borderRadius: 12,
    resizeMode: 'cover',
  },
  textbookGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  textbookCard: {
    width: '48%',
    marginBottom: 20,
    borderRadius: 12,
    overflow: 'hidden',
    backgroundColor: '#fff',
    ...shadowPresets.medium,
  },
  textbookImage: {
    width: '100%',
    height: 120,
    resizeMode: 'cover',
  },
  textbookTitle: {
    fontSize: 14,
    fontWeight: '500',
    color: '#333',
    padding: 12,
    textAlign: 'center',
  },
});

export default SynchronizedText;