import { TextInput, useColorScheme } from 'react-native'
import { Colors } from '../constants/Colors'

export default function ThemedTextInput({ style, ...props }) {
  const colorScheme = useColorScheme()
  const theme = Colors[colorScheme] ?? Colors.light

  return (
    <TextInput
      style={[
        {
          backgroundColor: theme.uiBackground,
          color: Colors.normalText, // 使用正常字体颜色
          padding: 20,
          borderRadius: 6,
        },
        style
      ]}
      placeholderTextColor={Colors.normalText} // 设置placeholder颜色
      {...props}
    />
  )
}