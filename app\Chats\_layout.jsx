import { Stack } from "expo-router"

export default function ChatsLayout() {
  return (
    <Stack screenOptions={{
      headerShown: false,
    }}>
      <Stack.Screen name="AllChats" options={{ title: "AllChats", headerShown: false }} />
      <Stack.Screen name="MyLike" options={{ title: "My<PERSON>ike", headerShown: false }} />
      <Stack.Screen name="SytemMessage" options={{ title: "SytemMessage", headerShown: false }} />
    </Stack>
  )
}
