import React, { useState } from 'react';
import { View, TextInput, TouchableOpacity, Switch, StyleSheet, ScrollView, Image, Alert } from 'react-native';
import { useRouter } from 'expo-router';
import ThemedView from '../../components/ThemedView';
import ThemedText from '../../components/ThemedText';
import { Colors } from '../../constants/Colors';

const CreateStudyRoomScreen = () => {
  const router = useRouter();
  const [roomName, setRoomName] = useState('');
  const [privacy, setPrivacy] = useState('public'); // 'public' or 'friends'
  const [onlyFemale, setOnlyFemale] = useState(false);
  const [roomPermission, setRoomPermission] = useState(false);
  const [allowTextChat, setAllowTextChat] = useState(true);
  const [allowMicrophone, setAllowMicrophone] = useState(false);
  const [studyCount, setStudyCount] = useState(4); // 默认4人
  const [isStudyCountExpanded, setIsStudyCountExpanded] = useState(false);

  const handleCreateRoom = () => {
    // 这里处理创建自习室的逻辑
    const roomData = {
      roomName,
      privacy,
      onlyFemale,
      roomPermission,
      allowTextChat,
      allowMicrophone,
      studyCount
    };
    console.log('创建自习室:', roomData);
    Alert.alert('创建成功', '自习室已创建', [
      {
        text: '确定',
        onPress: () => router.back()
      }
    ]);
  };

  return (
    <ThemedView style={{ flex: 1, backgroundColor: '#FFF8F3' }}>
      {/* 顶部标题栏 */}
      <View style={styles.header}>
        <TouchableOpacity
          onPress={() => router.back()}
          accessible={true}
          accessibilityRole="button"
          accessibilityLabel="返回"
        >
          <Image source={require('../../assets/FrameTwo.png')} style={styles.backIcon} />
        </TouchableOpacity>
        <ThemedText style={styles.headerTitle}>创建自习室</ThemedText>
        <View style={styles.headerPlaceholder} />
      </View>

      <ScrollView style={styles.scrollContainer} showsVerticalScrollIndicator={false}>
        {/* 页面标题 */}
        <ThemedText style={styles.pageTitle}>深度自习舱</ThemedText>
        <ThemedText style={styles.pageSubtitle}>AI屏蔽干扰，只留你和目标</ThemedText>

        <ThemedText style={styles.note}>您也可以在创建自习室后更改这些设置</ThemedText>

        {/* 自习室名称 */}
        <ThemedText style={styles.label}>自习室名称（选填）</ThemedText>
        <TextInput
          style={styles.input}
          placeholder="输入自习室名称"
          placeholderTextColor="#999"
          value={roomName}
          onChangeText={setRoomName}
        />

        {/* 自习室设置 */}
        <View style={styles.section}>
          <ThemedText style={styles.subTitle}>自习室设置</ThemedText>

          {/* 隐私设置 */}
          <ThemedText style={styles.settingLabel}>隐私设置</ThemedText>
          <View style={styles.row}>
            <TouchableOpacity
              style={[styles.privacyButton, privacy === 'public' && styles.activePrivacy]}
              onPress={() => setPrivacy('public')}
            >
              <ThemedText style={privacy === 'public' ? styles.activePrivacyText : styles.privacyText}>公共</ThemedText>
            </TouchableOpacity>
            <TouchableOpacity
              style={[styles.privacyButton, privacy === 'friends' && styles.activePrivacy]}
              onPress={() => setPrivacy('friends')}
            >
              <ThemedText style={privacy === 'friends' ? styles.activePrivacyText : styles.privacyText}>仅限好友</ThemedText>
            </TouchableOpacity>
          </View>

          {/* 分隔线 */}
          <View style={styles.divider} />

          {/* 自习人数选择 */}
          <TouchableOpacity
            style={styles.studyCountRow}
            onPress={() => setIsStudyCountExpanded(!isStudyCountExpanded)}
          >
            <ThemedText style={styles.optionText}>自习人数</ThemedText>
            <View style={styles.studyCountRight}>
              <ThemedText style={styles.studyCountValue}>{studyCount}人</ThemedText>
              <Image
                source={require('../../assets/Arrows_down.png')}
                style={[
                  styles.expandIcon,
                  isStudyCountExpanded && styles.expandIconRotated
                ]}
              />
            </View>
          </TouchableOpacity>

          {/* 人数选择展开区域 */}
          {isStudyCountExpanded && (
            <View style={styles.studyCountOptions}>
              {[2, 3, 4, 5, 6, 8, 10].map((count) => (
                <TouchableOpacity
                  key={count}
                  style={[
                    styles.countOption,
                    studyCount === count && styles.selectedCountOption
                  ]}
                  onPress={() => {
                    setStudyCount(count);
                    setIsStudyCountExpanded(false);
                  }}
                >
                  <ThemedText style={[
                    styles.countOptionText,
                    studyCount === count && styles.selectedCountOptionText
                  ]}>
                    {count}人
                  </ThemedText>
                </TouchableOpacity>
              ))}
            </View>
          )}

          {/* 其他选项设置 */}
          <View style={styles.optionRow}>
            <ThemedText style={styles.optionText}>仅允许女生加入</ThemedText>
            <Switch
              value={onlyFemale}
              onValueChange={setOnlyFemale}
              trackColor={{ false: "#767577", true: "#FFD29B" }}
            />
          </View>
          <View style={styles.optionRow}>
            <ThemedText style={styles.optionText}>自习室权限</ThemedText>
            <Switch
              value={roomPermission}
              onValueChange={setRoomPermission}
              trackColor={{ false: "#767577", true: "#FFD29B" }}
            />
          </View>
          <View style={styles.optionRow}>
            <ThemedText style={styles.optionText}>允许文字聊天</ThemedText>
            <Switch
              value={allowTextChat}
              onValueChange={setAllowTextChat}
              trackColor={{ false: "#767577", true: "#FFD29B" }}
            />
          </View>
          <View style={styles.optionRow}>
            <ThemedText style={styles.optionText}>允许使用麦克风</ThemedText>
            <Switch
              value={allowMicrophone}
              onValueChange={setAllowMicrophone}
              trackColor={{ false: "#767577", true: "#FFD29B" }}
            />
          </View>
        </View>

        {/* 用户限制说明 */}
        <ThemedText style={styles.description}>
          创建自习室后，您可以邀请朋友加入。或者，您也可以匹配一个学习格子，然后将一对一的学习会议转换为小组学习会议。
        </ThemedText>

        {/* 创建按钮 */}
        <TouchableOpacity style={styles.createButton} onPress={handleCreateRoom}>
          <ThemedText style={styles.createButtonText}>创建自习室</ThemedText>
        </TouchableOpacity>
      </ScrollView>
    </ThemedView>
  );
};

const styles = StyleSheet.create({
  // 顶部导航栏样式
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingTop: 60,
    paddingHorizontal: 16,
    backgroundColor: 'transparent',
    marginBottom: 24,
  },
  backIcon: {
    width: 28,
    height: 28,
    marginRight: 8,
  },
  headerTitle: {
    flex: 1,
    fontSize: 22,
    fontWeight: 'bold',
    color: Colors.light.title,
    textAlign: 'center',
  },
  headerPlaceholder: {
    width: 36,
    height: 28,
  },

  // 滚动容器
  scrollContainer: {
    flex: 1,
    paddingHorizontal: 16,
  },

  // 页面标题
  pageTitle: {
    fontSize: 16,
    fontWeight: 'medium',
    color: Colors.light.title,
    textAlign: 'center',
    marginBottom: 8,
  },
  pageSubtitle: {
    fontSize: 16,
    color: Colors.light.text,
    textAlign: 'center',
    marginBottom: 24,
  },

  // 表单元素
  note: {
    fontSize: 14,
    color: Colors.light.text,
    marginBottom: 20,
    textAlign: 'center',
  },
  label: {
    fontSize: 16,
    marginBottom: 8,
    color: Colors.light.title,
    fontWeight: '500',
  },
  input: {
    backgroundColor: 'white',
    padding: 15,
    borderRadius: 12,
    marginBottom: 20,
    borderWidth: 1,
    borderColor: '#E0E0E0',
    fontSize: 16,
    color: Colors.light.title,
  },

  // 卡片样式
  section: {
    marginBottom: 20,
    backgroundColor: 'white',
    borderRadius: 16,
    padding: 18,
    shadowColor: '#000',
    shadowOpacity: 0.04,
    shadowRadius: 8,
    shadowOffset: { width: 0, height: 2 },
    elevation: 2,
  },
  subTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 20,
    color: Colors.light.title,
  },
  settingLabel: {
    fontSize: 16,
    marginBottom: 12,
    color: Colors.light.title,
  },
  row: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 20,
  },
  divider: {
    height: 1,
    backgroundColor: '#F0F0F0',
    marginVertical: 20,
  },
  privacyButton: {
    flex: 1,
    padding: 15,
    borderRadius: 12,
    backgroundColor: '#F5F5F5',
    marginHorizontal: 6,
    alignItems: 'center',
  },
  activePrivacy: {
    backgroundColor: '#FFD29B',
  },
  privacyText: {
    fontSize: 16,
    color: Colors.light.title,
    fontWeight: '500',
  },
  activePrivacyText: {
    fontSize: 16,
    color: 'white',
    fontWeight: '500',
  },
  optionRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 15,
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
  },
  optionText: {
    fontSize: 16,
    color: Colors.light.title,
  },
  studyCountRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 15,
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
  },
  studyCountRight: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  studyCountValue: {
    fontSize: 16,
    color: Colors.light.text,
    marginRight: 8,
  },
  expandIcon: {
    width: 16,
    height: 16,
    tintColor: Colors.light.text,
  },
  expandIconRotated: {
    transform: [{ rotate: '180deg' }],
  },
  studyCountOptions: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    paddingVertical: 10,
    paddingHorizontal: 5,
  },
  countOption: {
    paddingVertical: 8,
    paddingHorizontal: 16,
    margin: 4,
    borderRadius: 20,
    backgroundColor: '#F5F5F5',
    borderWidth: 1,
    borderColor: 'transparent',
  },
  selectedCountOption: {
    backgroundColor: '#FFD29B',
    borderColor: '#E59B62',
  },
  countOptionText: {
    fontSize: 14,
    color: Colors.light.title,
    fontWeight: '500',
  },
  selectedCountOptionText: {
    color: '#E59B62',
    fontWeight: 'bold',
  },
  description: {
    fontSize: 14,
    color: Colors.light.text,
    marginBottom: 30,
    lineHeight: 22,
    textAlign: 'center',
    paddingHorizontal: 10,
  },
  createButton: {
    backgroundColor: '#FFD29B',
    padding: 18,
    borderRadius: 12,
    alignItems: 'center',
    marginBottom: 30,
    shadowColor: '#FFD29B',
    shadowOpacity: 0.3,
    shadowRadius: 8,
    shadowOffset: { width: 0, height: 4 },
    elevation: 4,
  },
  createButtonText: {
    color: 'white',
    fontSize: 18,
    fontWeight: 'bold',
  },
});

export default CreateStudyRoomScreen;