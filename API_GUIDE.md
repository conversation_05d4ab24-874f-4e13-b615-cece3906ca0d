# 统一API配置系统使用指南

## 概述

我们已经创建了一个统一的API配置系统，整合了所有分散的API配置文件。新系统提供了更好的维护性、一致性和扩展性。

## 新系统架构

### 1. 核心文件

- **`lib/apiConfig.js`** - 统一的网络配置管理
- **`lib/apiClient.js`** - 统一的HTTP客户端
- **`lib/apiServices.js`** - 业务API服务层
- **`lib/apiLegacy.js`** - 向后兼容层

### 2. 配置层次

```
apiServices (业务层)
    ↓
apiClient (HTTP客户端层)
    ↓
apiConfig (配置层)
```

## 使用方法

### 推荐方式：使用新的API服务

```javascript
import apiServices from '../lib/apiServices';

// 认证相关
const loginResult = await apiServices.auth.login(username, password);
const userProfile = await apiServices.auth.verifyUser();

// 内容相关
const indexData = await apiServices.content.getIndexData(userId);
const featuredList = await apiServices.content.getFeaturedContentList();

// 用户资料相关
const profileData = await apiServices.profile.getUserProfile();
const aiProfile = await apiServices.profile.saveUserTagAndGenerateProfile(selections);

// 标签相关
const tags = await apiServices.tag.getTagsByStepId(stepId);
```

### 兼容方式：使用旧接口

```javascript
// 仍然可以使用旧的导入方式，内部会自动使用新系统
import { getIndexData } from '../lib/contentApi';
import { login } from '../lib/api';
import { saveUserTagAndGenerateProfile } from '../lib/userProfile';

// 这些调用会自动转发到新的API系统
const data = await getIndexData();
const result = await login(username, password);
```

## 配置管理

### 网络配置

所有网络配置现在集中在 `lib/apiConfig.js` 中：

```javascript
const NETWORK_CONFIG = {
  development: {
    DEV_IP: '**************', // 统一的开发机器IP
    PORT: '8080',
    // 各平台具体配置...
  }
};
```

### 自定义IP配置

```javascript
import { saveCustomIP, getCustomIP, resetNetworkConfig } from '../lib/apiConfig';

// 保存自定义IP
await saveCustomIP('*************');

// 获取当前IP
const currentIP = await getCustomIP();

// 重置配置
await resetNetworkConfig();
```

## 特性

### 1. 自动图片URL转换

新系统会自动将localhost图片URL转换为实际IP：

```javascript
// 自动转换图片URL
const data = await apiServices.content.getIndexData(userId);
// 图片URL已自动从 localhost:8080 转换为 **************:8080
```

### 2. 统一错误处理

```javascript
try {
  const data = await apiServices.content.getIndexData();
} catch (error) {
  // 统一的错误格式和处理
  console.error('API调用失败:', error.message);
}
```

### 3. 自动重试机制

```javascript
// 网络请求失败时自动重试（最多2次）
const data = await apiServices.content.getIndexData();
```

### 4. 认证管理

```javascript
// 自动管理认证token
await apiServices.auth.login(username, password); // 自动保存token
const profile = await apiServices.profile.getUserProfile(); // 自动使用token
await apiServices.auth.logout(); // 自动清除token
```

## 迁移指南

### 步骤1：更新导入（推荐）

```javascript
// 旧方式
import { getIndexData } from '../lib/contentApi';
import { login } from '../lib/api';

// 新方式
import apiServices from '../lib/apiServices';
```

### 步骤2：更新API调用

```javascript
// 旧方式
const data = await getIndexData(userId);
const result = await login(username, password);

// 新方式
const data = await apiServices.content.getIndexData(userId);
const result = await apiServices.auth.login(username, password);
```

### 步骤3：移除旧文件（可选）

迁移完成后，可以删除以下旧文件：
- `lib/api.js`
- `lib/contentApi.js`
- `lib/userProfile.js`
- `lib/profileApi.js`
- `constants/index.js`（API相关部分）

## 调试工具

### 网络配置调试

```javascript
import { debugNetworkConfig } from '../lib/apiConfig';

// 查看当前网络配置
const config = await debugNetworkConfig();
console.log('当前配置:', config);
```

### API调用日志

新系统会自动输出详细的API调用日志：

```
[ios] 发起请求 (尝试 1/3): {url: "...", method: "GET", timeout: "15000ms"}
[ios] 响应成功 (耗时 234ms): {status: 200, statusText: "OK", ok: true}
```

## 扩展新API

### 添加新的API服务

在 `lib/apiServices.js` 中添加新的API分组：

```javascript
export const newApi = {
  async getNewData() {
    const response = await apiClient.get('/new-endpoint');
    return response.data;
  },
  
  async postNewData(data) {
    const response = await apiClient.post('/new-endpoint', data);
    return response.data;
  }
};
```

### 添加新的配置

在 `lib/apiConfig.js` 中添加新的配置项：

```javascript
const NETWORK_CONFIG = {
  development: {
    // 现有配置...
    NEW_SERVICE_PORT: '9000',
    // 新配置...
  }
};
```

## 最佳实践

1. **使用新的API服务层**：优先使用 `apiServices` 而不是直接调用 `apiClient`
2. **集中配置管理**：所有网络配置都在 `apiConfig.js` 中管理
3. **错误处理**：使用try-catch包装API调用
4. **类型安全**：为API响应定义TypeScript类型（如果使用TypeScript）
5. **测试**：为API调用编写单元测试

## 常见问题

### Q: 如何更改开发机器IP？
A: 修改 `lib/apiConfig.js` 中的 `DEV_IP` 配置，或使用 `saveCustomIP()` 函数。

### Q: 旧代码是否需要立即迁移？
A: 不需要，兼容层确保旧代码继续工作，但建议逐步迁移到新API。

### Q: 如何添加新的API端点？
A: 在 `lib/apiServices.js` 中添加新的方法，使用 `apiClient` 发起请求。

### Q: 图片URL转换如何工作？
A: 系统会自动检测localhost URL并替换为实际IP，无需手动处理。
