import React from "react";
import { View, StyleSheet } from "react-native";
import Svg, { Polygon } from 'react-native-svg';
import ThemedText from "./ThemedText";

function TriangleCard() {
  return (
    <View style={styles.triangleContainer}>
      <Svg width="60" height="60" viewBox="0 0 100 100">
        <Polygon points="50,0 100,100 0,100" fill="#4CAF50" />
      </Svg>
    </View>
  );
}

export default function IndustryInfo() {
  return (
    <View style={styles.industryInfo}>
      <View style={styles.industryInfoHeader}>
        <ThemedText style={styles.industryInfoTitle}>行业资讯</ThemedText>
        <ThemedText style={styles.industryInfoArrow}>{'>'}</ThemedText>
      </View>
      <View style={styles.triangleRow}>
        <TriangleCard />
        <TriangleCard />
        <TriangleCard />
        <TriangleCard />
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  industryInfo: {
    padding: 16,
  },
  industryInfoHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  industryInfoTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
  industryInfoArrow: {
    fontSize: 16,
    color: '#666',
  },
  triangleRow: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  triangleContainer: {
    width: 60,
    height: 60,
    margin: 8,
  },
}); 