// hooks/useGuestPermission.js - 游客权限管理Hook
import { useState, useCallback } from 'react';
import { useRouter } from 'expo-router';
import { useUser } from './useUser';
import apiServices from '../lib/apiServices';

/**
 * 游客权限管理Hook
 * 处理游客用户的页面访问权限和注册引导
 */
export const useGuestPermission = () => {
  const { user, logout } = useUser();
  const router = useRouter();
  const [showPermissionModal, setShowPermissionModal] = useState(false);
  const [currentFeature, setCurrentFeature] = useState({
    name: "此功能",
    description: "需要注册账号才能使用完整功能"
  });

  // 定义游客可访问的页面
  const guestAccessiblePages = {
    'index': {
      accessible: true,
      name: '首页',
      description: '浏览学习内容'
    },
    'community': {
      accessible: true,
      name: '社区',
      description: '查看社区动态（只读模式）'
    },
    'profile': {
      accessible: true,
      name: '个人中心',
      description: '查看基本信息（只读模式）'
    },
    'plans': {
      accessible: false,
      name: '学习计划',
      description: '个性化学习计划需要注册账号才能使用'
    }
  };

  // 定义受限功能的详细信息
  const restrictedFeatures = {
    'plans': {
      name: '学习计划',
      description: '制定个性化学习计划，跟踪学习进度，需要注册账号才能使用'
    },
    'profile-edit': {
      name: '编辑个人资料',
      description: '修改个人信息和偏好设置需要注册账号'
    },
    'community-interaction': {
      name: '社区互动',
      description: '点赞、评论、转发等社区互动功能需要注册账号'
    },
    'search': {
      name: '搜索功能',
      description: '搜索学习内容和用户需要注册账号'
    },
    'study-progress': {
      name: '学习进度',
      description: '保存和同步学习进度需要注册账号'
    },
    'favorites': {
      name: '收藏功能',
      description: '收藏喜欢的内容需要注册账号'
    },
    'settings': {
      name: '设置',
      description: '个性化设置需要注册账号'
    },
    'video-interaction': {
      name: '视频互动',
      description: '视频点赞、收藏、评论等功能需要注册账号'
    },
    'learning-tools': {
      name: '学习工具',
      description: '学习工具和个性化功能需要注册账号'
    },
    'user-data': {
      name: '用户数据',
      description: '保存和同步用户数据需要注册账号'
    }
  };

  /**
   * 检查游客是否可以访问指定页面
   * @param {string} pageName - 页面名称
   * @returns {boolean} - 是否可以访问
   */
  const canAccessPage = useCallback((pageName) => {
    if (!user?.isGuest) return true; // 非游客用户可以访问所有页面
    
    const pageConfig = guestAccessiblePages[pageName];
    return pageConfig ? pageConfig.accessible : false;
  }, [user]);

  /**
   * 检查游客是否可以使用指定功能
   * @param {string} featureName - 功能名称
   * @returns {boolean} - 是否可以使用
   */
  const canUseFeature = useCallback((featureName) => {
    if (!user?.isGuest) return true; // 非游客用户可以使用所有功能
    
    // 游客不能使用的功能
    const restrictedFeatureNames = Object.keys(restrictedFeatures);
    return !restrictedFeatureNames.includes(featureName);
  }, [user]);

  /**
   * 尝试访问页面，如果是游客且无权限则显示提示
   * @param {string} pageName - 页面名称
   * @param {Function} onSuccess - 成功访问时的回调
   * @returns {boolean} - 是否成功访问
   */
  const tryAccessPage = useCallback((pageName, onSuccess) => {
    if (canAccessPage(pageName)) {
      if (onSuccess) onSuccess();
      return true;
    }

    // 显示权限提示
    const pageConfig = guestAccessiblePages[pageName] || {};
    setCurrentFeature({
      name: pageConfig.name || pageName,
      description: pageConfig.description || '此功能需要注册账号才能使用'
    });
    setShowPermissionModal(true);
    return false;
  }, [canAccessPage]);

  /**
   * 尝试使用功能，如果是游客且无权限则显示提示
   * @param {string} featureName - 功能名称
   * @param {Function} onSuccess - 成功使用时的回调
   * @returns {boolean} - 是否成功使用
   */
  const tryUseFeature = useCallback((featureName, onSuccess) => {
    if (canUseFeature(featureName)) {
      if (onSuccess) onSuccess();
      return true;
    }

    // 显示权限提示
    const featureConfig = restrictedFeatures[featureName] || {};
    setCurrentFeature({
      name: featureConfig.name || featureName,
      description: featureConfig.description || '此功能需要注册账号才能使用'
    });
    setShowPermissionModal(true);
    return false;
  }, [canUseFeature]);

  /**
   * 游客按钮拦截器 - 所有交互按钮都应该使用这个
   * @param {Function} onPress - 原始的按钮点击处理函数
   * @param {string} featureName - 功能名称，用于显示对应的提示信息
   * @returns {Function} - 包装后的点击处理函数
   */
  const wrapGuestAction = useCallback((onPress, featureName = 'user-data') => {
    return () => {
      if (!user?.isGuest) {
        // 非游客用户，正常执行
        if (onPress) onPress();
        return;
      }

      // 游客用户，显示登录提示
      console.log('游客尝试使用功能:', featureName);
      const featureConfig = restrictedFeatures[featureName] || {};
      setCurrentFeature({
        name: featureConfig.name || '此功能',
        description: featureConfig.description || '此功能需要注册账号才能使用'
      });
      setShowPermissionModal(true);
    };
  }, [user, restrictedFeatures]);

  /**
   * 处理注册按钮点击
   * 清除游客数据并跳转到注册页面
   */
  const handleRegister = useCallback(async () => {
    try {
      console.log('游客选择注册，清除游客数据');
      
      // 关闭弹窗
      setShowPermissionModal(false);
      
      // 清除游客会话数据
      await apiServices.auth.clearGuestSession();
      
      // 退出当前用户状态
      await logout();
      
      // 跳转到注册页面
      router.replace('/(auth)/register');
      
      console.log('已清除游客数据，跳转到注册页面');
    } catch (error) {
      console.error('处理注册跳转失败:', error);
      // 即使出错也要跳转到注册页面
      router.replace('/register');
    }
  }, [logout, router]);

  /**
   * 关闭权限提示弹窗
   */
  const closePermissionModal = useCallback(() => {
    setShowPermissionModal(false);
  }, []);

  /**
   * 获取页面访问权限信息
   * @param {string} pageName - 页面名称
   * @returns {Object} - 权限信息
   */
  const getPagePermission = useCallback((pageName) => {
    const accessible = canAccessPage(pageName);
    const pageConfig = guestAccessiblePages[pageName] || {};
    
    return {
      accessible,
      name: pageConfig.name || pageName,
      description: pageConfig.description || '',
      isGuest: user?.isGuest || false
    };
  }, [canAccessPage, user]);

  /**
   * 获取功能使用权限信息
   * @param {string} featureName - 功能名称
   * @returns {Object} - 权限信息
   */
  const getFeaturePermission = useCallback((featureName) => {
    const accessible = canUseFeature(featureName);
    const featureConfig = restrictedFeatures[featureName] || {};
    
    return {
      accessible,
      name: featureConfig.name || featureName,
      description: featureConfig.description || '',
      isGuest: user?.isGuest || false
    };
  }, [canUseFeature, user]);

  return {
    // 状态
    isGuest: user?.isGuest || false,
    showPermissionModal,
    currentFeature,

    // 权限检查方法
    canAccessPage,
    canUseFeature,
    tryAccessPage,
    tryUseFeature,

    // 游客拦截器
    wrapGuestAction,

    // 权限信息获取
    getPagePermission,
    getFeaturePermission,

    // 事件处理
    handleRegister,
    closePermissionModal,

    // 配置信息
    guestAccessiblePages,
    restrictedFeatures
  };
};
