import React, { useState, useEffect } from 'react';
import { View, StyleSheet, TouchableOpacity, Image, PanResponder } from 'react-native';
import { useRouter } from 'expo-router';
import ThemedView from '../../components/ThemedView';
import ThemedText from '../../components/ThemedText';


const StudyroomA = () => {
  const router = useRouter();
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [studyTime, setStudyTime] = useState(0); // 学习时间（秒）

  // 手势处理器
  const panResponder = PanResponder.create({
    onMoveShouldSetPanResponder: (evt, gestureState) => {
      return Math.abs(gestureState.dx) > Math.abs(gestureState.dy) && Math.abs(gestureState.dx) > 10;
    },
    onPanResponderMove: (evt, gestureState) => {
      // 可以在这里添加拖拽动画效果
    },
    onPanResponderRelease: (evt, gestureState) => {
      // 左滑超过50px就收起菜单
      if (gestureState.dx < -50) {
        setIsMenuOpen(false);
      }
    },
  });

  // 计时器效果
  useEffect(() => {
    const timer = setInterval(() => {
      setStudyTime(prev => prev + 1);
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  // 格式化时间
  const formatTime = (seconds) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;
    return `${hours}h ${minutes}min ${secs}s`;
  };

  // Sample data for online users
  const onlineUsers = [
    { id: '1', name: 'Jessica', avatar: require('../../assets/Community_image/AOne.png') },
    { id: '2', name: 'Cary', avatar: require('../../assets/Community_image/ATwo.png') },
    { id: '3', name: 'Lucy', avatar: require('../../assets/Community_image/AThree.png') },
    { id: '4', name: 'Lucy', avatar: require('../../assets/Community_image/AFour.png') },
    { id: '5', name: 'Alex', avatar: require('../../assets/Community_image/AFive.png') },
  ];

  return (
    <ThemedView style={{ flex: 1, backgroundColor: '#FFF8F3' }}>
      {/* 安全视图 */}
      <View style={styles.safeArea} />

      {/* 顶部标题栏 */}
      <View style={styles.header}>
        <TouchableOpacity onPress={() => router.back()}>
          <Image source={require('../../assets/FrameTwo.png')} style={styles.backIcon} />
        </TouchableOpacity>
        <ThemedText style={styles.headerTitle}>自习室名称</ThemedText>

      </View>

      {/* 主要内容 */}
      <View style={styles.mainContent}>
        {/* 左侧侧菜单栏 */}
        <View style={styles.leftSidebar}>
          <TouchableOpacity
            style={styles.menuButton}
            onPress={() => setIsMenuOpen(!isMenuOpen)}
          >
            <View style={styles.verticalTextContainer}>
              <ThemedText style={styles.verticalText}>在</ThemedText>
              <ThemedText style={styles.verticalText}>线</ThemedText>
              <ThemedText style={styles.verticalText}>列</ThemedText>
              <ThemedText style={styles.verticalText}>表</ThemedText>
            </View>
          </TouchableOpacity>

          {/* 弹出菜单 */}
          {isMenuOpen && (
            <View style={styles.dropdownMenu} {...panResponder.panHandlers}>
              <ThemedText style={styles.menuTitle}>在线列表</ThemedText>
              {onlineUsers.map((user) => (
                <View key={user.id} style={styles.userItem}>
                  <Image source={user.avatar} style={styles.userAvatar} />
                  <ThemedText style={styles.userName}>{user.name}</ThemedText>
                </View>
              ))}
              {/* 底部收起箭头 */}
              <TouchableOpacity
                style={styles.collapseButton}
                onPress={() => setIsMenuOpen(false)}
              >
                <ThemedText style={styles.arrowText}>← 左滑收起</ThemedText>
              </TouchableOpacity>
            </View>
          )}
        </View>

        {/* 右侧卡片区域 */}
        <View style={styles.rightCards}>
          {onlineUsers.slice(0, 4).map((user, index) => (
            <View key={user.id} style={styles.studyCard}>
              <View style={styles.cardTop}>
                <View style={styles.cardUserInfo}>
                  <Image source={user.avatar} style={styles.cardAvatar} />
                  <ThemedText style={styles.cardUserName}>{user.name}</ThemedText>
                </View>
              </View>
              <View style={styles.cardMiddle} />
              <View style={styles.cardBottom}>
                <Image
                  source={require('../../assets/Community_image/Studying.png')}
                  style={styles.studyingIcon}
                />
              </View>
            </View>
          ))}
        </View>
      </View>

      {/* 底部计时器 */}
      <View style={styles.timerContainer}>
        <ThemedText style={styles.timerText}>
          当前学习时间: {formatTime(studyTime)}
        </ThemedText>
      </View>
    </ThemedView>
  );
};

const styles = StyleSheet.create({
  safeArea: {
    height: 44,
    backgroundColor: 'transparent',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: 'transparent',
  },
  backIcon: {
    width: 24,
    height: 24,
    tintColor: '#333',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    flex: 1,
    textAlign: 'center',
  },
  headerMenu: {
    fontSize: 20,
    color: '#333',
    fontWeight: 'bold',
  },
  mainContent: {
    flex: 1,
    flexDirection: 'row',
    paddingHorizontal: 16,
    paddingTop: 10,
  },
  leftSidebar: {
    width: 50,
    paddingRight: 16,
    position: 'relative',
  },
  menuButton: {
    backgroundColor: '#FFE7CD',
    paddingVertical: 15,
    paddingHorizontal: 4,
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: 20,
    width: 30,
  },
  verticalTextContainer: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  verticalText: {
    fontSize: 12,
    color: '#333',
    fontWeight: 'bold',
    lineHeight: 16,
    textAlign: 'center',
  },
  dropdownMenu: {
    position: 'absolute',
    top: 0,
    left: -20,
    width: 120,
    backgroundColor: '#FFE7CD',
    borderRadius: 8,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 6,
    elevation: 5,
    zIndex: 10,
  },
  menuTitle: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 12,
    textAlign: 'center',
    paddingBottom: 8,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  userItem: {
    flexDirection: 'column',
    alignItems: 'center',
    marginBottom: 12,
  },
  userAvatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
    marginBottom: 4,
  },
  userName: {
    fontSize: 10,
    color: '#333',
    textAlign: 'center',
  },
  collapseButton: {
    marginTop: 12,
    paddingVertical: 8,
    paddingHorizontal: 12,
    backgroundColor: '#f0f0f0',
    borderRadius: 6,
    alignItems: 'center',
    borderTopWidth: 1,
    borderTopColor: '#e0e0e0',
  },
  arrowText: {
    fontSize: 12,
    color: '#666',
    fontWeight: 'bold',
  },
  cardUserInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  cardAvatar: {
    width: 20,
    height: 20,
    borderRadius: 10,
    marginRight: 6,
  },
  cardUserName: {
    fontSize: 10,
    color: '#333',
    fontWeight: 'bold',
  },
  timerContainer: {
    position: 'absolute',
    bottom: 60,
    left: 0,
    right: 0,
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    paddingVertical: 8,
    paddingHorizontal: 16,
    marginHorizontal: 16,
    borderRadius: 8,
  },
  timerText: {
    fontSize: 14,
    color: '#333',
    fontWeight: 'bold',
  },
  rightCards: {
    flex: 1,
    justifyContent: 'flex-start',
    alignItems: 'flex-end',
    paddingVertical: 20,
  },
  studyCard: {
    width: 250,
    height: 130,
    marginBottom: 16,
    borderRadius: 8,
    overflow: 'hidden',
  },
  cardTop: {
    height: '30%',
    backgroundColor: '#fff',
    padding: 8,
    justifyContent: 'flex-start',
    alignItems: 'flex-start',
  },
  cardMiddle: {
    height: '50%',
    backgroundColor: '#f5f5f5',
  },
  cardBottom: {
    height: '20%',
    backgroundColor: '#DFC09E',
    justifyContent: 'flex-start',
    alignItems: 'flex-end',
    paddingRight: 8,
    paddingTop: 0,
    position: 'relative',
  },
  studyingIcon: {
    width: 80,
    height: 80,
    resizeMode: 'contain',
    position: 'absolute',
    right: 8,
    top: -60,
  },


});

export default StudyroomA;