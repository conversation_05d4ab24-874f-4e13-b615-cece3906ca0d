import { useUser } from '../../hooks/useUser'
import { useRouter } from 'expo-router'
import { useEffect } from 'react'

import ThemedLoader from '../ThemedLoader'

const GuestOnly = ({ children }) => {
  const { user, authChecked } = useUser()
  const router = useRouter()

  useEffect(() => {
    if (authChecked && user !== null) {
      console.log('GuestOnly: 检测到已登录用户，返回上一页或跳转到dashboard');
      // 尝试返回上一页，如果没有历史记录则跳转到dashboard
      if (router.canGoBack()) {
        router.back();
      } else {
        router.replace("/(dashboard)/?tab=index");
      }
    }
  }, [user, authChecked])

  if (!authChecked || user) {
    return null; // 直接返回空，避免加载动画闪屏
  }

  return children
}

export default GuestOnly