import { useUser } from '../../hooks/useUser'
import { useRouter } from 'expo-router'
import { useEffect } from 'react'
import AsyncStorage from '@react-native-async-storage/async-storage'

import ThemedLoader from '../ThemedLoader'

const GuestOnly = ({ children }) => {
  const { user, authChecked } = useUser()
  const router = useRouter()

  useEffect(() => {
    const handleRedirect = async () => {
      if (authChecked && user !== null) {
        console.log('GuestOnly: 检测到已登录用户，检查是否是第一次登录');

        try {
          // 检查是否是第一次登录（刚刚完成登录）
          const isFirstLogin = await AsyncStorage.getItem('isFirstLogin');

          if (isFirstLogin === 'true') {
            // 第一次登录，清除标记并根据用户类型跳转
            await AsyncStorage.removeItem('isFirstLogin');

            if (user.isGuest) {
              console.log('GuestOnly: 第一次游客登录 → dashboard index');
              router.replace("/(dashboard)/?tab=index");
            } else {
              console.log('GuestOnly: 第一次正常用户登录 → plans');
              router.replace("/(dashboard)/plans?tab=plans");
            }
          } else {
            // 不是第一次登录，用户可能是刷新了auth页面
            console.log('GuestOnly: 非第一次登录，返回dashboard');
            router.replace("/(dashboard)/?tab=index");
          }
        } catch (error) {
          console.error('GuestOnly: 重定向逻辑出错:', error);
          // 出错时的默认行为
          router.replace("/(dashboard)/?tab=index");
        }
      }
    };

    handleRedirect();
  }, [user, authChecked])

  if (!authChecked || user) {
    return null; // 直接返回空，避免加载动画闪屏
  }

  return children
}

export default GuestOnly