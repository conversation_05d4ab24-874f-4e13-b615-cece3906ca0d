# 后端服务配置指南

## 问题诊断

根据您的日志，网络请求超时了。这通常意味着：
1. 请求能够发送到服务器
2. 但服务器没有及时响应
3. 或者响应被阻止了

## 后端服务配置检查

### 1. 确认服务监听地址

**❌ 错误配置 (只监听localhost):**
```javascript
app.listen(8080, 'localhost'); // 只能从本机访问
app.listen(8080, '127.0.0.1'); // 只能从本机访问
```

**✅ 正确配置 (监听所有接口):**
```javascript
app.listen(8080, '0.0.0.0'); // 可以从网络中的其他设备访问
// 或者
app.listen(8080); // 默认监听所有接口
```

### 2. CORS配置

确保后端允许跨域请求：

```javascript
// Express.js 示例
const cors = require('cors');

app.use(cors({
  origin: ['http://localhost:3000', 'http://*************:3000'], // 允许的源
  credentials: true
}));

// 或者允许所有源 (仅开发环境)
app.use(cors());
```

### 3. 防火墙配置

#### Windows:
1. 打开 Windows Defender 防火墙
2. 点击"允许应用或功能通过 Windows Defender 防火墙"
3. 添加端口 8080 的入站规则

#### Mac:
```bash
# 检查防火墙状态
sudo /usr/libexec/ApplicationFirewall/socketfilterfw --getglobalstate

# 如果启用了防火墙，添加端口规则
sudo pfctl -f /etc/pf.conf
```

#### Linux:
```bash
# Ubuntu/Debian
sudo ufw allow 8080

# CentOS/RHEL
sudo firewall-cmd --permanent --add-port=8080/tcp
sudo firewall-cmd --reload
```

### 4. 网络连接测试

#### 在开发机器上测试:
```bash
# 测试本地服务
curl http://localhost:8080/api/index

# 测试网络接口
curl http://*************:8080/api/index
```

#### 在手机浏览器中测试:
访问: `http://*************:8080/api/index`

### 5. 常见后端框架配置

#### Express.js:
```javascript
const express = require('express');
const cors = require('cors');
const app = express();

// CORS配置
app.use(cors());

// 解析JSON
app.use(express.json());

// 健康检查端点
app.get('/health', (req, res) => {
  res.json({ status: 'ok', timestamp: new Date().toISOString() });
});

// API路由
app.get('/api/index', (req, res) => {
  res.json({ message: 'API working' });
});

// 监听所有接口
app.listen(8080, '0.0.0.0', () => {
  console.log('Server running on http://0.0.0.0:8080');
});
```

#### Spring Boot:
```yaml
# application.yml
server:
  address: 0.0.0.0  # 监听所有接口
  port: 8080

# CORS配置
spring:
  web:
    cors:
      allowed-origins: "*"
      allowed-methods: "*"
      allowed-headers: "*"
```

#### Django:
```python
# settings.py
ALLOWED_HOSTS = ['*']  # 允许所有主机 (仅开发环境)

# CORS配置
CORS_ALLOW_ALL_ORIGINS = True  # 仅开发环境
```

### 6. 调试步骤

1. **检查服务状态:**
   ```bash
   # 检查端口是否被监听
   netstat -an | grep 8080
   # 或
   lsof -i :8080
   ```

2. **检查服务日志:**
   - 查看后端服务的控制台输出
   - 确认是否收到请求
   - 检查是否有错误信息

3. **网络连通性测试:**
   ```bash
   # 从开发机器测试
   telnet ************* 8080
   
   # 或使用nc
   nc -zv ************* 8080
   ```

4. **使用网络诊断工具:**
   - 在应用中使用 `NetworkTestButton` 组件
   - 查看详细的诊断结果

### 7. 超时问题解决

如果请求超时，检查：

1. **服务器响应时间:**
   - 数据库查询是否过慢
   - 是否有阻塞操作
   - 服务器资源是否充足

2. **网络延迟:**
   - WiFi信号强度
   - 网络拥塞情况
   - 路由器配置

3. **请求大小:**
   - 响应数据是否过大
   - 是否需要分页或压缩

### 8. 快速验证清单

- [ ] 后端服务监听 `0.0.0.0:8080`
- [ ] 防火墙允许端口 8080
- [ ] CORS配置正确
- [ ] 在开发机器上可以访问 `http://localhost:8080`
- [ ] 在开发机器上可以访问 `http://*************:8080`
- [ ] 在手机浏览器中可以访问 `http://*************:8080`
- [ ] 后端日志显示收到请求
- [ ] 网络诊断工具显示连接成功

### 9. 示例健康检查端点

添加一个简单的健康检查端点来测试连接：

```javascript
// 添加到您的后端服务
app.get('/health', (req, res) => {
  res.json({
    status: 'ok',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    memory: process.memoryUsage()
  });
});
```

然后在手机浏览器中访问: `http://*************:8080/health`
