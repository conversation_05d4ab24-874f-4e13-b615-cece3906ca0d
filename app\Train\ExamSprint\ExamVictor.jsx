import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  StyleSheet,
  TouchableOpacity,
  Text,
  StatusBar,
  Animated,
  Dimensions,
  Platform
} from 'react-native';
import { useRouter } from 'expo-router';
import ThemedText from '../../../components/ThemedText';
import Svg, { Circle } from 'react-native-svg';
const AnimatedCircle = Animated.createAnimatedComponent(Circle);

const { width, height } = Dimensions.get('window');

const ExamVictor = () => {
  const router = useRouter();
  const [score, setScore] = useState(0);
  const [displayScore, setDisplayScore] = useState(0);
  const [correctCount] = useState(4); // 模拟答对4题
  const [totalQuestions] = useState(5);

  // 动画值
  const confettiAnims = useRef([...Array(20)].map(() => ({
    translateY: new Animated.Value(-100),
    translateX: new Animated.Value(Math.random() * width),
    rotate: new Animated.Value(0),
    opacity: new Animated.Value(1),
  }))).current;

  const progressAnim = useRef(new Animated.Value(0)).current;
  const scoreCountAnim = useRef(new Animated.Value(0)).current;

  // 计算分数
  useEffect(() => {
    const calculatedScore = Math.round((correctCount / totalQuestions) * 100);
    setScore(calculatedScore);
  }, [correctCount, totalQuestions]);

  // 启动动画
  useEffect(() => {
    // 彩带动画
    const confettiAnimations = confettiAnims.map((anim) =>
      Animated.parallel([
        Animated.timing(anim.translateY, {
          toValue: height + 100,
          duration: 3000 + Math.random() * 2000,
          useNativeDriver: Platform.OS !== 'web',
        }),
        Animated.timing(anim.rotate, {
          toValue: 360 * (2 + Math.random() * 3),
          duration: 3000 + Math.random() * 2000,
          useNativeDriver: Platform.OS !== 'web',
        }),
        Animated.sequence([
          Animated.delay(2000),
          Animated.timing(anim.opacity, {
            toValue: 0,
            duration: 1000,
            useNativeDriver: Platform.OS !== 'web',
          })
        ])
      ])
    );

    Animated.parallel(confettiAnimations).start();

    // 进度环动画
    setTimeout(() => {
      Animated.timing(progressAnim, {
        toValue: score / 100,
        duration: 2000,
        useNativeDriver: false,
      }).start();
    }, 500);

    // 分数计数动画
    setTimeout(() => {
      Animated.timing(scoreCountAnim, {
        toValue: score,
        duration: 2000,
        useNativeDriver: false,
      }).start();
    }, 500);

    // 监听分数变化
    const listener = scoreCountAnim.addListener(({ value }) => {
      setDisplayScore(Math.round(value));
    });

    return () => {
      scoreCountAnim.removeListener(listener);
    };
  }, [score]);

  // 彩带颜色
  const confettiColors = [
    '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7',
    '#DDA0DD', '#98D8C8', '#F7DC6F', '#BB8FCE', '#85C1E9'
  ];

  // 进度环参数
  const circleRadius = 80;
  const circleCircumference = 2 * Math.PI * circleRadius;

  return (
    <View style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor="#F5F5F5" />

      {/* 彩带动画 */}
      {confettiAnims.map((anim, index) => (
        <Animated.View
          key={index}
          style={[
            styles.confetti,
            {
              backgroundColor: confettiColors[index % confettiColors.length],
              transform: [
                { translateX: anim.translateX },
                { translateY: anim.translateY },
                {
                  rotate: anim.rotate.interpolate({
                    inputRange: [0, 360],
                    outputRange: ['0deg', '360deg']
                  })
                }
              ],
              opacity: anim.opacity,
            }
          ]}
        />
      ))}

      {/* 主要内容 */}
      <View style={styles.content}>
        <ThemedText style={styles.title}>祝贺你!</ThemedText>

        {/* 进度环 */}
        <View style={styles.progressContainer}>
          <Svg width={200} height={200} style={styles.progressSvg}>
            {/* 背景圆环 */}
            <Circle
              cx={100}
              cy={100}
              r={circleRadius}
              stroke="#FFEEDB"
              strokeWidth={8}
              fill="transparent"
            />
            {/* 进度圆环 */}
            <AnimatedCircle
              cx={100}
              cy={100}
              r={circleRadius}
              stroke="#FFC47B"
              strokeWidth={8}
              fill="transparent"
              strokeDasharray={circleCircumference}
              strokeDashoffset={progressAnim.interpolate({
                inputRange: [0, 1],
                outputRange: [circleCircumference, 0]
              })}
              strokeLinecap="round"
              transform={`rotate(-90 100 100)`}
            />
          </Svg>

          {/* 分数显示 */}
          <View style={styles.scoreContainer}>
            <Text style={styles.scoreNumber}>{displayScore}</Text>
            <Text style={styles.scoreTotal}>/ 100</Text>
          </View>
        </View>

        <ThemedText style={styles.scoreLabel}>你的分数</ThemedText>

        <View style={styles.resultDetails}>
          <ThemedText style={styles.resultText}>
            {correctCount}道 正确 和 {totalQuestions - correctCount}道 错误
          </ThemedText>
          <ThemedText style={styles.encouragement}>
            请保持并继续努力!
          </ThemedText>
        </View>

        {/* 按钮区域 */}
        <View style={styles.buttonContainer}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => router.push('/Train/ExamSprint')}
          >
            <Text style={styles.backButtonText}>返回课程</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.progressButton}
            onPress={() => {
              // 这里可以添加查看进度的逻辑
              console.log('View My Progress pressed');
            }}
          >
            <Text style={styles.progressButtonText}>查看进度</Text>
          </TouchableOpacity>
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFF8F3',
  },
  confetti: {
    position: 'absolute',
    width: 8,
    height: 8,
    zIndex: 1000,
  },
  content: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 20,
    paddingTop: 100,
  },
  title: {
    fontSize: 32,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 60,
    textAlign: 'center',
  },
  progressContainer: {
    position: 'relative',
    marginBottom: 30,
  },
  progressSvg: {
    transform: [{ rotate: '0deg' }],
  },
  scoreContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
  },
  scoreNumber: {
    fontSize: 48,
    fontWeight: 'bold',
    color: '#333',
  },
  scoreTotal: {
    fontSize: 20,
    color: '#666',
    marginTop: -5,
  },
  scoreLabel: {
    fontSize: 24,
    fontWeight: '600',
    color: '#333',
    marginBottom: 30,
  },
  resultDetails: {
    alignItems: 'center',
    marginBottom: 60,
  },
  resultText: {
    fontSize: 16,
    color: '#666',
    marginBottom: 8,
    textAlign: 'center',
  },
  encouragement: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
  },
  buttonContainer: {
    width: '100%',
    paddingHorizontal: 20,
  },
  backButton: {
    backgroundColor: '#FFEEDB',
    borderRadius: 25,
    paddingVertical: 15,
    paddingHorizontal: 30,
    marginBottom: 15,
    alignItems: 'center',
  },
  backButtonText: {
    fontSize: 16,
    color: '#FFC47B',
    fontWeight: '600',
  },
  progressButton: {
    backgroundColor: '#FFC47B',
    borderRadius: 25,
    paddingVertical: 15,
    paddingHorizontal: 30,
    alignItems: 'center',
  },
  progressButtonText: {
    fontSize: 16,
    color: '#FFF',
    fontWeight: '600',
  },
});

export default ExamVictor;