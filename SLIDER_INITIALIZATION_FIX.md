# 滑块初始化问题修复

## 🐛 问题分析

错误信息：`cannot access 'setSliderPosition' before initialization`

**原因**：在useEffect中使用了 `setSliderPosition` 函数，但该函数在后面才定义，导致了初始化顺序问题。

## ✅ 修复方案

### 方案1：简化初始化逻辑

将滑块初始化逻辑简化，避免复杂的函数依赖：

```javascript
// 1. 简化初始值设置
const sliderTranslateX = useSharedValue(0)
const sliderScale = useSharedValue(1)
const sliderColor = useSharedValue(tabColors.index.slider)

// 2. 定义设置函数
const setSliderPosition = useCallback((tabName, immediate = false) => {
  // 函数实现...
}, [sliderTranslateX, sliderColor, screenWidth]);

// 3. 在函数定义后使用
useEffect(() => {
  if (user) {
    const initialTab = user.isGuest ? 'index' : 'plans';
    setActiveTab(initialTab);
    setSliderPosition(initialTab, true);
  }
}, [user, setSliderPosition]);
```

### 方案2：使用内联逻辑

直接在useEffect中计算位置，避免函数依赖：

```javascript
useEffect(() => {
  if (user) {
    const initialTab = user.isGuest ? 'index' : 'plans';
    const tabIndexMap = { 'index': 0, 'plans': 1, 'more': 2, 'community': 3, 'profile': 4 };
    const tabIndex = tabIndexMap[initialTab] || 0;
    const tabBarContentWidth = screenWidth - 40;
    const actualTabWidth = tabBarContentWidth / 5;
    const targetPosition = actualTabWidth * tabIndex;
    
    setActiveTab(initialTab);
    sliderTranslateX.value = targetPosition;
    sliderColor.value = tabColors[initialTab]?.slider || tabColors.index.slider;
  }
}, [user, sliderTranslateX, sliderColor, screenWidth]);
```

## 🔧 推荐实现

我推荐使用方案2，因为它：
- 避免了函数依赖问题
- 逻辑更直接清晰
- 减少了代码复杂度
- 确保初始化顺序正确

## 📝 实施步骤

1. 简化滑块初始值
2. 使用内联逻辑进行初始化
3. 保持setSliderPosition函数用于后续交互
4. 确保函数定义在使用之前

这样可以避免初始化顺序问题，同时保持代码的清晰性。
