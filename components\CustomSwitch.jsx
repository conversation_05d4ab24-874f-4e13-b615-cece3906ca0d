import React from 'react';
import { View, Text, TouchableOpacity, Animated } from 'react-native';

const CustomSwitch = ({ 
  value, 
  onValueChange, 
  text, 
  trackColorOff = '#FFD29B', 
  trackColorOn = '#FDAC54',
  thumbColor = '#fff',
  width = 120,
  height = 32
}) => {
  const animatedValue = React.useRef(new Animated.Value(value ? 1 : 0)).current;

  React.useEffect(() => {
    Animated.timing(animatedValue, {
      toValue: value ? 1 : 0,
      duration: 200,
      useNativeDriver: false,
    }).start();
  }, [value]);

  const handlePress = () => {
    onValueChange(!value);
  };

  const thumbPosition = animatedValue.interpolate({
    inputRange: [0, 1],
    outputRange: [2, width - height + 2],
  });

  const trackColor = animatedValue.interpolate({
    inputRange: [0, 1],
    outputRange: [trackColorOff, trackColorOn],
  });

  return (
    <TouchableOpacity onPress={handlePress} activeOpacity={0.8}>
      <Animated.View
        style={{
          width: width,
          height: height,
          borderRadius: height / 2,
          backgroundColor: trackColor,
          justifyContent: 'center',
          alignItems: 'center',
          position: 'relative',
        }}
      >
        {/* 文字 */}
        <Text
          style={{
            fontSize: 12,
            color: '#444',
            fontWeight: '600',
            position: 'absolute',
            zIndex: 1,
            textAlign: 'center',
            paddingHorizontal: 4,
          }}
          numberOfLines={1}
          ellipsizeMode="tail"
        >
          {text}
        </Text>
        
        {/* 滑块按钮 */}
        <Animated.View
          style={{
            position: 'absolute',
            left: thumbPosition,
            width: height - 4,
            height: height - 4,
            borderRadius: (height - 4) / 2,
            backgroundColor: thumbColor,
            // 外阴影效果
            shadowColor: '#000',
            shadowOffset: {
              width: 0,
              height: 2,
            },
            shadowOpacity: 0.25,
            shadowRadius: 3.84,
            elevation: 5,
          }}
        />
      </Animated.View>
    </TouchableOpacity>
  );
};

export default CustomSwitch;
